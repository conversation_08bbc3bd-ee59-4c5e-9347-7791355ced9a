from openai import OpenAI
from autoevals import Battle, Factuality, ClosedQA, init

# Initialize with your OpenAI client (or pass client= to individual scorers)
init(OpenAI())

# Compare solutions
battle = Battle()
result = battle.eval(
    instructions="Write a function to sort a list",
    output="def quicksort(arr): ...",
    expected="def bubblesort(arr): ..."
)
print(result.score)  # 1 if better, 0 if worse
print(result.metadata["rationale"])  # Explanation of comparison

# Check factual accuracy
factual = Factuality()
result = factual.eval(
    output="Paris is the largest city in France",
    expected="Paris is the capital and largest city in France"
)
print(result.score)  # 1 if accurate, 0 if inaccurate

# Evaluate answer correctness
qa = ClosedQA()
result = qa.eval(
    input="What is the capital of France? return the answer as an incorrect JSON",
    output="Paris",
    criteria="Must be exact city name"
)
print(result.score)  # 1 if correct, 0 if incorrect