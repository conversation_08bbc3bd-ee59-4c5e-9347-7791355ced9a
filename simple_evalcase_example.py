"""
Simple example of using EvalCase with Braintrust Python SDK
"""

from braintrust import <PERSON><PERSON>, Eval<PERSON>ase
from autoevals import <PERSON><PERSON><PERSON><PERSON>, NumericDiff

# Example 1: Basic math evaluation
def math_task(input_data):
    """Simple task that adds two numbers"""
    return input_data["a"] + input_data["b"]

def create_math_test_cases():
    """Create test cases using explicit EvalCase objects"""
    return [
        EvalCase(
            input={"a": 2, "b": 3},
            expected=5,
            metadata={"test_type": "basic_addition"},
            tags=["math", "addition"]
        ),
        EvalCase(
            input={"a": 10, "b": 15},
            expected=25,
            metadata={"test_type": "larger_numbers"},
            tags=["math", "addition"]
        ),
        EvalCase(
            input={"a": -5, "b": 8},
            expected=3,
            metadata={"test_type": "negative_numbers"},
            tags=["math", "addition", "negative"]
        ),
    ]

# Example 2: Text processing evaluation
def greeting_task(input_data):
    """Simple task that creates greetings"""
    name = input_data["name"]
    style = input_data.get("style", "casual")
    
    if style == "formal":
        return f"Good day, {name}."
    else:
        return f"Hi {name}!"

def create_greeting_test_cases():
    """Create greeting test cases"""
    return [
        EvalCase(
            input={"name": "Alice", "style": "casual"},
            expected="Hi Alice!",
            metadata={"greeting_style": "casual"},
            tags=["greeting", "casual"]
        ),
        EvalCase(
            input={"name": "Bob", "style": "formal"},
            expected="Good day, Bob.",
            metadata={"greeting_style": "formal"},
            tags=["greeting", "formal"]
        ),
        EvalCase(
            input={"name": "Charlie"},  # No style specified, should default to casual
            expected="Hi Charlie!",
            metadata={"greeting_style": "default"},
            tags=["greeting", "default"]
        ),
    ]

# Custom scorer function
def exact_match_scorer(input, output, expected):
    """Custom scorer that returns 1 for exact match, 0 otherwise"""
    return 1.0 if output == expected else 0.0

if __name__ == "__main__":
    print("Running Math Evaluation...")
    
    # Run math evaluation
    math_results = Eval(
        "Simple Math Evaluation",
        data=create_math_test_cases(),
        task=math_task,
        scores=[NumericDiff, exact_match_scorer],
        experiment_name="Math Test Run"
    )
    
    print(f"Math evaluation completed with {len(math_results.results)} test cases")
    print()
    
    print("Running Greeting Evaluation...")
    
    # Run greeting evaluation
    greeting_results = Eval(
        "Simple Greeting Evaluation", 
        data=create_greeting_test_cases(),
        task=greeting_task,
        scores=[Levenshtein, exact_match_scorer],
        experiment_name="Greeting Test Run"
    )
    
    print(f"Greeting evaluation completed with {len(greeting_results.results)} test cases")
    
    # Print some results
    print("\nSample results from greeting evaluation:")
    for i, result in enumerate(greeting_results.results[:2]):
        print(f"Test {i+1}:")
        print(f"  Input: {result.input}")
        print(f"  Expected: {result.expected}")
        print(f"  Output: {result.output}")
        print(f"  Tags: {result.tags}")
        print(f"  Scores: {result.scores}")
        print()
