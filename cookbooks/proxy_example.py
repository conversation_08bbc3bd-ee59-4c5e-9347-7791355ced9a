from openai import OpenAI

client = OpenAI(
    base_url="https://api.braintrust.dev/v1/proxy",
    api_key="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJicmFpbnRydXN0X3Byb3h5IiwiYXVkIjoiYnJhaW50cnVzdF9wcm94eSIsImp0aSI6ImJ0X3RtcDo1N2M4MDE2Yy1iYzQ3LTQ1NTYtYmU2OC1kYWY2YTk0YTQwZjMiLCJidCI6eyJtb2RlbCI6ImdwdC00by1taW5pIiwic2VjcmV0IjoiNGV3OFVkU2JyYVpSQ1pnZCtYeDF0bjBoSlhSekoxWU0yNzMyYXppLzJMUT0ifSwiaWF0IjoxNzUyMDc5MDQyLCJleHAiOjE3NTIwNzk2NDJ9.J4XR2Oi_HyorajYWU8qnK3ek64ThkM-LL327B21NpbY",
)

response = client.chat.completions.create(
    model="gpt-4o-mini",
    messages=[{"role": "user", "content": "What is a proxy?"}],
)
print(response.choices[0].message.content)