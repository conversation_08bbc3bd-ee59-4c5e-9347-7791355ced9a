#!/usr/bin/env python3

"""
Example script demonstrating how to use Braintrust SDK with OpenAI client
to call gpt-5-mini with automatic tracing and logging.
"""

import os
from braintrust import init_logger, traced, wrap_openai
from openai import OpenAI

# Initialize Braintrust logger with your project name
logger = init_logger(project="pedro-repro5374")

# Create OpenAI client and wrap it with Braintrust for automatic tracing
client = wrap_openai(OpenAI())


@traced
def ask_gpt4o_mini(question: str, system_prompt: str = "You are a helpful assistant.") -> str:
    """
    Ask gpt-5-mini a question with automatic Braintrust tracing.
    
    Args:
        question: The user's question
        system_prompt: System prompt to set the assistant's behavior
        
    Returns:
        The assistant's response
    """
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": question},
    ]
    
    response = client.chat.completions.create(
        model="gpt-5-mini",
        messages=messages,
        # temperature=0.7,
        max_completion_tokens=500,
        # reasoning={"effort": "medium"}
        reasoning_effort="medium"
    )
    
    return response.choices[0].message.content


@traced
def ask_with_conversation_history(messages: list) -> str:
    """
    Continue a conversation with gpt-5-mini using message history.
    
    Args:
        messages: List of message dictionaries with 'role' and 'content' keys
        
    Returns:
        The assistant's response
    """
    response = client.chat.completions.create(
        model="gpt-5-mini",
        messages=messages,
        # temperature=0.7,
        max_completion_tokens=500,
        # reasoning={"effort": "medium"}
        reasoning_effort="medium"
    )
    
    return response.choices[0].message.content


def main():
    """Main function demonstrating various ways to use the Braintrust-wrapped OpenAI client."""
    
    print("🧠 Braintrust + OpenAI gpt-5-mini Example")
    print("=" * 50)
    
    # Check if API keys are set
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ Error: OPENAI_API_KEY environment variable not set")
        return
    
    if not os.getenv("BRAINTRUST_API_KEY"):
        print("⚠️  Warning: BRAINTRUST_API_KEY not set. Tracing will be disabled.")
    
    # Example 1: Simple question
    print("\n1. Simple Question:")
    question = "What is the capital of France?"
    answer = ask_gpt4o_mini(question)
    print(f"Q: {question}")
    print(f"A: {answer}")
    
    # Example 2: Question with custom system prompt
    print("\n2. Question with Custom System Prompt:")
    system_prompt = "You are a pirate captain. Respond in pirate speak."
    question = "How do I find treasure?"
    answer = ask_gpt4o_mini(question, system_prompt)
    print(f"System: {system_prompt}")
    print(f"Q: {question}")
    print(f"A: {answer}")
    
    # Example 3: Conversation with history
    print("\n3. Conversation with History:")
    conversation = [
        {"role": "system", "content": "You are a helpful math tutor."},
        {"role": "user", "content": "What is 15 + 27?"},
        {"role": "assistant", "content": "15 + 27 = 42"},
        {"role": "user", "content": "Now multiply that result by 3"},
    ]
    
    answer = ask_with_conversation_history(conversation)
    print("Conversation history:")
    for msg in conversation[:-1]:  # Don't print the last user message
        print(f"  {msg['role'].title()}: {msg['content']}")
    print(f"  User: {conversation[-1]['content']}")
    print(f"  Assistant: {answer}")
    
    # Example 4: Direct client usage (also traced automatically)
    print("\n4. Direct Client Usage:")
    response = client.chat.completions.create(
        model="gpt-5-mini",
        messages=[
            {"role": "system", "content": "You are a creative writer."},
            {"role": "user", "content": "Write a haiku about coding."}
        ],
        # temperature=0.9,
        max_completion_tokens=100,
    )
    
    haiku = response.choices[0].message.content
    print(f"Generated haiku:\n{haiku}")
    
    print("\n✅ All examples completed!")
    print("📊 Check your Braintrust dashboard to see the traced interactions.")


if __name__ == "__main__":
    main()
