#!/usr/bin/env python3
"""
Simple example of fetching an existing experiment using Braintrust SDK.
This version avoids accessing private attributes and handles the missing name property gracefully.

Usage:
    python simple_fetch_experiment_clean.py

Make sure to:
1. Install braintrust: pip install braintrust
2. Set your BRAINTRUST_API_KEY environment variable
3. Update PROJECT_NAME and EXPERIMENT_NAME below
"""

import braintrust

# Configuration - update these with your actual project and experiment names
PROJECT_NAME = "pedro-project1"
EXPERIMENT_NAME = "simple-test"

def main():
    try:
        print(f"Fetching experiment '{EXPERIMENT_NAME}' from project '{PROJECT_NAME}'...")
        
        # Fetch existing experiment (read-only)
        experiment = braintrust.init(
            project=PROJECT_NAME,
            experiment=EXPERIMENT_NAME,
            open=True  # This makes it read-only and fetches existing experiment
        )
        
        # Print basic experiment info
        print(f"✓ Experiment fetched successfully!")
        print(f"Experiment name: {EXPERIMENT_NAME}")
        print(f"Experiment ID: {experiment.id}")
        
        # Iterate through experiment records
        print("\nExperiment records:")
        print("-" * 40)
        
        record_count = 0
        for i, record in enumerate(experiment.fetch()):
            record_count += 1
            print(f"Record {record_count}:")
            
            # Show available fields in the record
            if 'input' in record:
                print(f"  Input: {record['input']}")
            if 'output' in record:
                print(f"  Output: {record['output']}")
            if 'expected' in record:
                print(f"  Expected: {record['expected']}")
            if 'scores' in record:
                print(f"  Scores: {record['scores']}")
            if 'metadata' in record:
                print(f"  Metadata: {record['metadata']}")
            if 'created' in record:
                print(f"  Created: {record['created']}")
            
            print()  # Empty line for readability
            
            # Stop after first 3 records for demo
            if i >= 2:
                print("... (showing first 3 records)")
                break
        
        if record_count == 0:
            print("No records found in the experiment.")
            print("This might be because:")
            print("- The experiment exists but has no logged data")
            print("- The experiment name or project name is incorrect")
        else:
            print(f"Total records shown: {min(record_count, 3)}")
            
        # Show how to use the experiment as a dataset
        print("\n" + "=" * 50)
        print("Converting to dataset format:")
        dataset_records = list(experiment.as_dataset())
        print(f"Dataset records available: {len(dataset_records)}")
        
        if dataset_records:
            print("Sample dataset record:")
            sample = dataset_records[0]
            print(f"  Input: {sample.get('input')}")
            print(f"  Expected: {sample.get('expected')}")
            
    except ValueError as e:
        if "not found" in str(e):
            print(f"❌ Error: {e}")
            print("\nTroubleshooting:")
            print(f"- Check that project '{PROJECT_NAME}' exists")
            print(f"- Check that experiment '{EXPERIMENT_NAME}' exists in that project")
            print("- Verify you have access to the project")
        else:
            print(f"❌ Error: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        print("\nTroubleshooting:")
        print("- Make sure BRAINTRUST_API_KEY environment variable is set")
        print("- Check your internet connection")
        print("- Verify your API key is valid")

if __name__ == "__main__":
    main()
