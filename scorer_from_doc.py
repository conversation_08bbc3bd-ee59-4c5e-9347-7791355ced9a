import braintrust
import pydantic
 
project = braintrust.projects.create(name="pedro-project1")
 
 
class Input(pydantic.BaseModel):
    output: str
    expected: str
 
 
def handler(output: str, expected: str) -> int:
    return 1 if output == expected else 0
 
 
project.scorers.create(
    name="Equality scorer3",
    slug="equality-scorer3",
    description="An equality scorer",
    parameters=Input,
    handler=handler,
)
 
 
project.scorers.create(
    name="Equality LLM scorer4",
    slug="equality-llm-scorer4",
    description="An equality LLM scorer",
    messages=[
        {
            "role": "user",
            "content": 'Return "A" if {{output}} is equal to {{expected}}, and "B" otherwise.',
        },
    ],
    model="gpt-4o",
    use_cot=True,
    choice_scores={"A": 1, "B": 0},
)

project.scorers.create(
    name="Equality scorer5",
    slug="equality-scorer5",
    description="An equality scorer",
    parameters=Input,
    handler=handler,
)
 
 
project.scorers.create(
    name="Equality LLM scorer6",
    slug="equality-llm-scorer6",
    description="An equality LLM scorer",
    messages=[
        {
            "role": "user",
            "content": 'Return "A" if {{output}} is equal to {{expected}}, and "B" otherwise.',
        },
    ],
    model="gpt-4o",
    use_cot=True,
    choice_scores={"A": 1, "B": 0},
)

project.scorers.create(
    name="Equality scorer7",
    slug="equality-scorer7",
    description="An equality scorer",
    parameters=Input,
    handler=handler,
)
 
 
project.scorers.create(
    name="Equality LLM scorer8",
    slug="equality-llm-scorer8",
    description="An equality LLM scorer",
    messages=[
        {
            "role": "user",
            "content": 'Return "A" if {{output}} is equal to {{expected}}, and "B" otherwise.',
        },
    ],
    model="gpt-4o",
    use_cot=True,
    choice_scores={"A": 1, "B": 0},
)

project.scorers.create(
    name="Equality scorer9",
    slug="equality-scorer9",
    description="An equality scorer",
    parameters=Input,
    handler=handler,
)
 
 
project.scorers.create(
    name="Equality LLM scorer10",
    slug="equality-llm-scorer10",
    description="An equality LLM scorer",
    messages=[
        {
            "role": "user",
            "content": 'Return "A" if {{output}} is equal to {{expected}}, and "B" otherwise.',
        },
    ],
    model="gpt-4o",
    use_cot=True,
    choice_scores={"A": 1, "B": 0},
)