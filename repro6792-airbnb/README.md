# Demo Strands Agent - Personal Daily Planner

This demo simulates the behavior of the `ato_dp` agent with a simplified, shareable use case for vendor collaboration (Braintrust, Strands AWS teams).

## Overview

The demo implements a **Personal Daily Planner** that:
- Uses an orchestrator agent to coordinate multiple subagents
- Each subagent accesses 2-5 tools
- Integrates with OpenTelemetry and Braintrust for comprehensive logging
- Contains no confidential Airbnb data

## File Structure

- `assets.py` - Shared setup code including:
  - `FailFastOnToolError` hook implementation
  - `setup_llm_model()` - LLM initialization helper
  - `opentelemetry_init()` - OpenTelemetry and Braintrust logging setup
- `demo_strands_workflow.py` - Working example with successful tool calls
- `tool_failing_strands_workflow.py` - Example demonstrating tool failure handling
- `requirements.txt` - Python dependencies for external users
- `README.md` - This file

## Architecture

```
┌─────────────────────────────────────┐
│   Daily Plan Aggregator             │
│   (Orchestrator Agent)              │
└─────────────┬───────────────────────┘
              │
       ┌──────┴──────┐
       │             │
┌──────▼──────┐ ┌───▼──────────────┐
│  Outdoor    │ │  Indoor          │
│  Activity   │ │  Activity        │
│  Planner    │ │  Planner         │
│  (Subagent) │ │  (Subagent)      │
└──────┬──────┘ └───┬──────────────┘
       │            │
       │            ├─ get_office_calendar
       ├─ get_weather
       ├─ current_time (built-in)
       └─ calculator (built-in)
                    ├─ get_gym_calendar
                    └─ current_time (built-in)
```

## Workflow

1. **Outdoor Activity Planner** (subagent):
   - Checks weather forecast using `get_weather()`
   - Uses `current_time()` and `calculator()` (Strands built-in tools)
   - Recommends outdoor activities based on weather

2. **Indoor Activity Planner** (subagent):
   - Checks office schedule using `get_office_calendar()`
   - Checks gym availability using `get_gym_calendar()`
   - Uses `current_time()` (Strands built-in tool)
   - Recommends indoor activities

3. **Daily Plan Aggregator** (orchestrator):
   - Receives outputs from both subagents
   - Creates comprehensive daily plan with:
     - Wake-up time recommendations
     - Office/outdoor/gym schedule for the week
     - Priority score (1-10)

## Setup

### 1. Install Dependencies

**Option A: Using requirements.txt (Recommended for vendors)**

```bash
# Create a virtual environment (recommended)
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

**Option B: Install from ml_models repository (For Airbnb developers)**

```bash
# From the ml_models root directory
pip install -e .[maestro]
```

**Important for External Users:**
If you're using requirements.txt, you need to modify `assets.py` to remove Airbnb-specific imports:

1. Open `assets.py`
2. Comment out lines 95-97:
```python
# from airbnb_litellm import initialize
#
# initialize(model_id)
```

### 2. Configure Environment

Create a `.env` file in this directory with your API credentials:

```bash
# Braintrust configuration for observability
BRAINTRUST_API_KEY=your_braintrust_api_key
BRAINTRUST_PROJECT_ID=your_project_id
OTEL_EXPORTER_OTLP_ENDPOINT=https://api.braintrust.dev/otel/v1/traces

# LiteLLM configuration (if not using Airbnb's setup)
# Add your LLM provider credentials here, e.g.:
# AWS_ACCESS_KEY_ID=your_aws_key
# AWS_SECRET_ACCESS_KEY=your_aws_secret
# AWS_REGION_NAME=us-west-2
# Or for OpenAI:
# OPENAI_API_KEY=your_openai_key
```

### 3. Run the Demo

```bash
python demo_strands_workflow.py
```

## Output

The demo will:
1. Initialize OpenTelemetry and Braintrust integration
2. Run both subagents in sequence
3. Aggregate results in the orchestrator
4. Display:
   - Daily plan summary
   - Detailed daily schedule
   - Priority score
   - Subagent analysis details
5. Send traces and logs to Braintrust

## Logging and Observability

### OpenTelemetry
- Traces are automatically captured using `@tracer.start_as_current_span()` and [Strands Agents built-in OpenTelemetry integration](https://strandsagents.com/latest/documentation/docs/user-guide/observability-evaluation/traces/?h=opentelemet#opentelemetry-integration)
- All agent interactions are logged
- Tool calls are tracked

### Console Output
- Enabled by default for demo purposes
- Shows real-time agent activity and decisions

## Contact

For questions or issues with this demo, please reach out to #airbnb-braintrust-extpart on Slack
