import braintrust
import os
import autoevals
from openai import OpenAI
from braintrust import Eva<PERSON>, wrap_openai, invoke

client = wrap_openai(
    OpenAI(
        base_url="https://api.braintrust.dev/v1/proxy",
        api_key=os.environ["BRAINTRUST_API_KEY"],
    )
)
project_name = "pedro-SupportChatbot"

dataset = [
    {
        "input": "Why did my package disappear after tracking showed it was delivered?",
        "metadata": {"category": "shipping"},
    },
    {
        "input": "Your product smells like burnt rubber - what’s wrong with it?",
        "metadata": {"category": "product"},
    },
    {
        "input": "I ordered 3 items but only got 1, where’s the rest?",
        "metadata": {"category": "shipping"},
    },
    {
        "input": "Why does your app crash every time I try to check out?",
        "metadata": {"category": "tech"},
    },
    {
        "input": "My refund was supposed to be here 2 weeks ago - what’s the holdup?",
        "metadata": {"category": "returns"},
    },
    {
        "input": "Your instructions say ‘easy setup’ but it took me 3 hours!",
        "metadata": {"category": "product"},
    },
    {
        "input": "Why does your delivery guy keep leaving packages at the wrong house?",
        "metadata": {"category": "shipping"},
    },
    {
        "input": "The discount code you sent me doesn’t work - fix it!",
        "metadata": {"category": "sales"},
    },
    {
        "input": "Your support line hung up on me twice - what’s going on?",
        "metadata": {"category": "support"},
    },
    {
        "input": "Why is your website saying my account doesn’t exist when I just made it?",
        "metadata": {"category": "tech"},
    },
]

prompt="""
    Evaluate if the response aligns with our brand guidelines (Y/N):
    1. **Positive Tone**: Uses upbeat language, avoids negativity (e.g., "We’re thrilled to help!" vs. "That’s your problem").
    2. **Proactive Approach**: Offers a clear next step or solution (e.g., "We’ll track it now!" vs. vague promises).
    3. **Apologetic When Appropriate**: Acknowledges issues with empathy (e.g., "So sorry for the mix-up!" vs. ignoring the complaint).
    4. **Solution-Oriented**: Focuses on fixing the issue for the customer (e.g., "Here’s how we’ll make it right!" vs. excuses).
    5. **Professionalism**: There should be no profanity, or emojis.
 
    Response: {{output}}
 
 
    Only give a Y if all the criteria are met. If one is missing and it should be there, give a N.
    """

def run_chat_completion2(input):
    llm_obj = autoevals.LLMClassifier(
        name="classifier",
        model="gpt-4o",
        prompt_template=prompt,
        choice_scores={"Yes": 1, "No": 0},
        use_cot=True
    )
    return llm_obj(input=input['Input'], output=input['Output'])

def run_braintrust_eval2(prompt_name):
    braintrust.Eval(
        name="pedro-project1",
        data=dataset,
        task=lambda input: run_chat_completion2(
            input
        ),
        scores=[
            correctness_judge
        ],
        experiment_name=prompt_name,
        max_concurrency=3
    )

def correctness_judge(output,expected): 
    return 1 if output.metadata['choice'] == expected else 0

run_braintrust_eval2("llmclassifiertest")

# eval_task = Eval(
#     project_name,
#     data=lambda: dataset,
#     task=task_v3,
#     scores=[brand_alignment_scorer],
#     experiment_name="prompt_v3",
# )