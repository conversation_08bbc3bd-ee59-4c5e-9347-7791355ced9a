#!/usr/bin/env python3
"""
Example script showing how to fetch an existing dataset using the Braintrust SDK.

This script demonstrates:
1. How to authenticate with Braintrust
2. How to fetch an existing dataset by project and dataset name
3. How to iterate through dataset records
4. How to access dataset metadata and properties

Prerequisites:
- Install braintrust: pip install braintrust
- Set BRAINTRUST_API_KEY environment variable or provide it as a parameter
- Have an existing dataset in your Braintrust project
"""

import os
import braintrust
from typing import Optional


def fetch_dataset_example(
    project_name: str,
    dataset_name: str,
    api_key: Optional[str] = None,
    org_name: Optional[str] = None,
    app_url: Optional[str] = None
):
    """
    Fetch an existing dataset from Braintrust.
    
    Args:
        project_name: Name of the project containing the dataset
        dataset_name: Name of the dataset to fetch
        api_key: Braintrust API key (optional, will use BRAINTRUST_API_KEY env var if not provided)
        org_name: Organization name (optional)
        app_url: Braintrust app URL (optional, defaults to https://www.braintrust.dev)
    
    Returns:
        Dataset object that can be used to access records and metadata
    """
    
    # Initialize the dataset - this will fetch the existing dataset if it exists
    dataset = braintrust.init_dataset(
        project=project_name,
        name=dataset_name,
        api_key=api_key,
        org_name=org_name,
        app_url=app_url
    )
    
    return dataset


def explore_dataset(dataset):
    """
    Explore and display information about the dataset.
    
    Args:
        dataset: Braintrust Dataset object
    """
    
    print(f"Dataset ID: {dataset.id}")
    print(f"Dataset Name: {dataset.name}")
    print(f"Project: {dataset.project.name} (ID: {dataset.project.id})")
    
    # Access dataset metadata
    print(f"Dataset metadata: {dataset.data}")
    
    # Count records
    record_count = 0
    print("\nDataset Records:")
    print("-" * 50)
    
    # Iterate through all records in the dataset
    for record in dataset.fetch():
        record_count += 1
        print(f"Record {record_count}:")
        
        # Display common fields that are typically present in dataset records
        if 'input' in record:
            print(f"  Input: {record['input']}")
        if 'expected' in record:
            print(f"  Expected: {record['expected']}")
        if 'output' in record:  # For legacy datasets
            print(f"  Output: {record['output']}")
        if 'metadata' in record:
            print(f"  Metadata: {record['metadata']}")
        if 'tags' in record:
            print(f"  Tags: {record['tags']}")
        
        print()  # Empty line for readability
        
        # Limit output for demo purposes
        if record_count >= 5:
            print(f"... (showing first 5 records out of many)")
            break
    
    print(f"Total records processed: {record_count}")


def main():
    """
    Main function demonstrating how to fetch and explore a dataset.
    """
    
    # Configuration - modify these values for your specific dataset
    PROJECT_NAME = "your-project-name"  # Replace with your project name
    DATASET_NAME = "your-dataset-name"  # Replace with your dataset name
    
    # Optional: specify API key directly (otherwise uses BRAINTRUST_API_KEY env var)
    API_KEY = os.getenv("BRAINTRUST_API_KEY")
    
    if not API_KEY:
        print("Warning: No API key found. Set BRAINTRUST_API_KEY environment variable.")
        print("You can also pass api_key parameter to init_dataset().")
        return
    
    try:
        print(f"Fetching dataset '{DATASET_NAME}' from project '{PROJECT_NAME}'...")
        
        # Fetch the existing dataset
        dataset = fetch_dataset_example(
            project_name=PROJECT_NAME,
            dataset_name=DATASET_NAME,
            api_key=API_KEY
        )
        
        print("Dataset fetched successfully!")
        print("=" * 60)
        
        # Explore the dataset
        explore_dataset(dataset)
        
        print("\n" + "=" * 60)
        print("Dataset exploration complete!")
        
        # Example: Using the dataset in an evaluation
        print("\nExample: Using this dataset in an evaluation:")
        print("```python")
        print("from braintrust import Eval")
        print("")
        print("# Fetch your dataset")
        print(f"dataset = braintrust.init_dataset(project='{PROJECT_NAME}', name='{DATASET_NAME}')")
        print("")
        print("# Use it in an evaluation")
        print("Eval(")
        print("    'My Evaluation',")
        print("    data=dataset,  # Use the fetched dataset")
        print("    task=lambda input: your_model_function(input),")
        print("    scores=[your_scoring_function]")
        print(")")
        print("```")
        
    except Exception as e:
        print(f"Error fetching dataset: {e}")
        print("\nTroubleshooting tips:")
        print("1. Make sure the project name and dataset name are correct")
        print("2. Verify your API key is valid")
        print("3. Check that you have access to the specified project and dataset")
        print("4. Ensure the dataset exists in Braintrust")


if __name__ == "__main__":
    main()
