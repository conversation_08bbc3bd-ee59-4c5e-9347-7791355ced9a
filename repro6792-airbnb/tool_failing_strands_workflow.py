"""
Demo Strands Agent Workflow for Personal Daily Planner

This demo simulates the behavior of the ato_dp agent with a simplified use case:
- Orchestrator agent that coordinates subagents
- Subagents that use multiple tools
- Integration with OpenTelemetry and Braintrust for logging

Purpose: Shareable demo for vendors (Braintrust, Strands AWS teams) without confidential data.
"""

import logging
import os
from datetime import datetime
from typing import Any, Callable

from dotenv import load_dotenv
from opentelemetry import trace
from pydantic import BaseModel, Field
from strands import Agent
from strands.tools.decorator import tool
from strands_tools import calculator, current_time

# Import shared assets
from assets import FailFastOnToolError, opentelemetry_init, setup_llm_model

# Load environment variables from .env file at module level
# Get the directory where this script is located
script_dir = os.path.dirname(os.path.abspath(__file__))
env_path = os.path.join(script_dir, ".env")
load_dotenv(env_path)


# Set up logging
logging.basicConfig(level=logging.INFO, format="%(levelname)s | %(name)s | %(message)s")
logger = logging.getLogger(__name__)
# Set maximum logging level for strands.models
logging.getLogger("strands.models").setLevel(logging.DEBUG)

# Set up OpenTelemetry tracer
tracer = trace.get_tracer(__name__)

# Model configuration
_claude_sonnet = "anthropic/claude-sonnet-4-5"
_default_model_id = _claude_sonnet


# ============================================================================
# MOCK DATA STORES (Hash-table lookups for demo purposes)
# ============================================================================

WEATHER_DATA = {
    "mon": "sunny",
    "tues": "cloudy",
    "wed": "rainy",
    "thurs": "sunny",
    "fri": "windy",
    "sat": "stormy",
    "sun": "sunny",
}

OFFICE_CALENDAR = {
    "mon": "9AM-6PM",
    "tues": "9AM-6PM",
    "wed": "Closed",
    "thurs": "9AM-6PM",
    "fri": "9AM-5PM",
    "sat": "Closed",
    "sun": "Closed",
}

GYM_CALENDAR = {
    "mon": "6AM-10PM",
    "tues": "6AM-10PM",
    "wed": "6AM-10PM",
    "thurs": "6AM-10PM",
    "fri": "6AM-10PM",
    "sat": "8AM-8PM",
    "sun": "8AM-8PM",
}


# ============================================================================
# MOCK TOOLS
# ============================================================================


@tool
def get_weather(agent: Agent, day: str) -> str:
    """
    Get weather forecast for a specific day.

    :param day: Day of the week (mon, tues, wed, thurs, fri, sat, sun)
    :return: Weather description for the day
    """
    logger.info(f"get_weather called: day={day}, attempting to get weather but service will fail")

    # Always raise an exception to simulate weather service failure
    raise Exception("Weather service not available")


@tool
def get_office_calendar(agent: Agent, day: str) -> str:
    """
    Get office opening hours for a specific day.

    :param day: Day of the week (mon, tues, wed, thurs, fri, sat, sun)
    :return: Office hours for the day
    """
    day_lower = day.lower().strip()
    hours = OFFICE_CALENDAR.get(day_lower, "unknown")
    logger.info(f"get_office_calendar called: day={day}, hours={hours}")
    return f"Office hours on {day}: {hours}"


@tool
def get_gym_calendar(agent: Agent, day: str) -> str:
    """
    Get gym opening hours for a specific day.

    :param day: Day of the week (mon, tues, wed, thurs, fri, sat, sun)
    :return: Gym hours for the day
    """
    day_lower = day.lower().strip()
    hours = GYM_CALENDAR.get(day_lower, "unknown")
    logger.info(f"get_gym_calendar called: day={day}, hours={hours}")
    return f"Gym hours on {day}: {hours}"


# ============================================================================
# PYDANTIC MODELS FOR STRUCTURED OUTPUT
# ============================================================================


class ActivityPlan(BaseModel):
    """Base model for activity planning."""

    analysis: str = Field(
        description="Detailed analysis of the activity planning",
        default="",
    )
    recommendations: str = Field(
        description="Specific recommendations for activities",
        default="",
    )


class DailyPlanReport(BaseModel):
    """Final report for daily planning."""

    detailed_plan: str = Field(
        description="Detailed daily plan with timing and activities",
        default="",
    )
    summary: str = Field(
        description="Brief executive summary of the daily plan (up to 100 words)",
        default="",
    )
    priority_score: int = Field(
        description="Priority score from 1-10 indicating how well the plan aligns with user goals (1=lowest, 10=highest)",
        default=-1,
    )


class DailyPlanReportWithDetails(DailyPlanReport):
    """Extended report including subagent outputs."""

    outdoor_activity_analysis: str = Field(description="Result from outdoor activity planner agent")
    indoor_activity_analysis: str = Field(description="Result from indoor activity planner agent")


# ============================================================================
# HELPER FUNCTION FOR STRUCTURED OUTPUT WITH FALLBACK
# ============================================================================


def structured_agent_output_with_fallback(
    agent: Agent,
    output_model: type[BaseModel],
    input_message: str,
    max_retries: int = 1,
) -> BaseModel:
    """
    Get structured output from an agent with validation error handling and fallback.

    Args:
        agent: The strands Agent instance to use for generation
        output_model: The Pydantic model class for structured output
        input_message: The input message/prompt to send to the agent
        max_retries: Maximum number of retries on ValidationError (default: 1)

    Returns:
        Instance of output_model with structured data or fallback values
    """
    retry_message = input_message

    for attempt in range(max_retries + 1):
        try:
            return agent.structured_output(output_model, retry_message)
        except Exception as e:
            logger.error(f"Validation error in agent output (attempt {attempt + 1}): {e}")
            if attempt < max_retries:
                error_snippet = str(e)[:100]
                retry_message = f"{input_message}\n\nPrevious validation error: {error_snippet}"
                continue
            break

    return output_model()


# ============================================================================
# OUTDOOR ACTIVITY PLANNER AGENT
# ============================================================================


def mk_outdoor_activity_planner_agent(
    user_preferences: str,
    model_id: str | None = None,
    callback_handler: Callable[..., Any] | None = None,
) -> Agent:
    """
    Create an outdoor activity planner agent.

    Args:
        user_preferences: User's preferences and goals
        model_id: Optional model ID for the LiteLLMModel
        callback_handler: Optional callback handler for agent events

    Returns:
        Strands Agent instance for outdoor activity planning
    """
    system_prompt = """You are an Outdoor Activity Planner agent.

Your role is to analyze weather conditions and suggest outdoor activities based on user preferences.

Use the available tools to:
1. Check the weather forecast for relevant days
2. Use current_time to understand what day it is today
3. Use calculator if needed for time calculations

Provide detailed recommendations for outdoor activities considering weather conditions and user goals."""

    model_id = model_id or _default_model_id
    model = setup_llm_model(model_id)

    state = {
        "user_preferences": user_preferences,
    }

    tools = [get_weather, current_time, calculator]

    agent_kwargs = {
        "state": state,
        "tools": tools,
        "model": model,
        "system_prompt": system_prompt,
        "name": "OutdoorActivityPlannerAgent",
        "description": "Agent to plan outdoor activities based on weather and user preferences.",
        "hooks": [FailFastOnToolError()],
    }

    if callback_handler is not None:
        agent_kwargs["callback_handler"] = callback_handler

    agent = Agent(**agent_kwargs)
    return agent


def analyze_outdoor_activities(
    user_preferences: str,
    model_id: str | None = None,
) -> str:
    """
    Analyze and plan outdoor activities.

    Args:
        user_preferences: User's preferences and goals
        model_id: Optional model ID for the LiteLLMModel

    Returns:
        str: The outdoor activity analysis result
    """
    agent = mk_outdoor_activity_planner_agent(
        user_preferences=user_preferences,
        model_id=model_id,
    )

    prompt = f"""Based on the user's preferences: {user_preferences}

Analyze the weather for the upcoming week and recommend outdoor activities.
Consider which days are best for outdoor activities based on weather conditions."""

    result = agent(prompt=prompt)
    result_str = str(result)

    return result_str


# ============================================================================
# INDOOR ACTIVITY PLANNER AGENT
# ============================================================================


def mk_indoor_activity_planner_agent(
    user_preferences: str,
    model_id: str | None = None,
    callback_handler: Callable[..., Any] | None = None,
) -> Agent:
    """
    Create an indoor activity planner agent.

    Args:
        user_preferences: User's preferences and goals
        model_id: Optional model ID for the LiteLLMModel
        callback_handler: Optional callback handler for agent events

    Returns:
        Strands Agent instance for indoor activity planning
    """
    system_prompt = """You are an Indoor Activity Planner agent.

Your role is to analyze office and gym schedules and suggest indoor activities based on user preferences.

Use the available tools to:
1. Check the office calendar for work schedule
2. Check the gym calendar for workout availability
3. Use current_time to understand what day it is today

Provide detailed recommendations for indoor activities including office work and gym sessions considering user goals."""

    model_id = model_id or _default_model_id
    model = setup_llm_model(model_id)

    state = {
        "user_preferences": user_preferences,
    }

    tools = [get_office_calendar, get_gym_calendar, calculator, current_time]

    agent_kwargs = {
        "state": state,
        "tools": tools,
        "model": model,
        "system_prompt": system_prompt,
        "name": "IndoorActivityPlannerAgent",
        "description": "Agent to plan indoor activities based on office/gym schedules and user preferences.",
        "hooks": [FailFastOnToolError()],
    }

    if callback_handler is not None:
        agent_kwargs["callback_handler"] = callback_handler

    agent = Agent(**agent_kwargs)
    return agent


def analyze_indoor_activities(
    user_preferences: str,
    model_id: str | None = None,
) -> str:
    """
    Analyze and plan indoor activities.

    Args:
        user_preferences: User's preferences and goals
        model_id: Optional model ID for the LiteLLMModel

    Returns:
        str: The indoor activity analysis result
    """
    agent = mk_indoor_activity_planner_agent(
        user_preferences=user_preferences,
        model_id=model_id,
    )

    prompt = f"""Based on the user's preferences: {user_preferences}

Analyze the office and gym schedules for the upcoming week and recommend indoor activities.
Consider work days and gym workout opportunities."""

    result = agent(prompt=prompt)
    result_str = str(result)

    return result_str


# ============================================================================
# ORCHESTRATOR AGENT - DAILY PLAN AGGREGATOR
# ============================================================================


def mk_daily_plan_aggregator(model_id: str | None = None) -> Agent:
    """
    Create an orchestrator agent that aggregates subagent results.

    Args:
        model_id: Optional model ID for the LiteLLMModel

    Returns:
        Strands Agent instance for aggregating daily plans
    """
    system_prompt = """You are a Daily Plan Aggregator agent.

Your role is to synthesize inputs from outdoor and indoor activity planners to create a comprehensive daily plan.

You will receive:
1. Outdoor Activity Analysis - recommendations for outdoor activities based on weather
2. Indoor Activity Analysis - recommendations for indoor activities including office and gym

Your task is to:
1. Create a detailed daily plan that specifies:
   - Wake-up time recommendation
   - Which days to go to office (if any)
   - Which days for outdoor activities (if any)
   - Which days for gym workouts (if any)
   - Rest and recovery days
2. Provide a brief summary of the plan
3. Assign a priority score (1-10) based on how well the plan aligns with user goals

Format the output with specific times and activities for each day of the week."""

    model_id = model_id or _default_model_id
    model = setup_llm_model(model_id)
    model.params = {"temperature": 0.0}

    agent = Agent(
        name="Daily Plan Aggregator",
        description="Aggregates results from activity planning agents and provides a comprehensive daily plan.",
        system_prompt=system_prompt,
        model=model,
        hooks=[FailFastOnToolError()],
    )
    return agent


def aggregate_daily_plan(
    outdoor_activity_analysis: str,
    indoor_activity_analysis: str,
) -> DailyPlanReport:
    """
    Aggregate results from activity planning agents into a comprehensive daily plan.

    Args:
        outdoor_activity_analysis: Result from outdoor activity planner agent
        indoor_activity_analysis: Result from indoor activity planner agent

    Returns:
        DailyPlanReport: Aggregated daily plan with summary and priority score
    """
    aggregator_agent = mk_daily_plan_aggregator()
    input_message = f"""Outdoor Activity Analysis:
{outdoor_activity_analysis}

Indoor Activity Analysis:
{indoor_activity_analysis}"""

    return structured_agent_output_with_fallback(
        agent=aggregator_agent,
        output_model=DailyPlanReport,
        input_message=input_message,
        max_retries=1,
    )


# ============================================================================
# MAIN WORKFLOW FUNCTION
# ============================================================================


@tracer.start_as_current_span("run_daily_planner")
def run_daily_planner(
    user_preferences: str,
    model_id: str | None = None,
) -> DailyPlanReportWithDetails:
    """
    Main workflow function that orchestrates the daily planning process.

    This function:
    1. Calls the outdoor activity planner subagent
    2. Calls the indoor activity planner subagent
    3. Aggregates results from both subagents
    4. Returns a comprehensive daily plan

    Args:
        user_preferences: User's mood, preferences, and goals
        model_id: Optional model ID for the LiteLLMModel

    Returns:
        DailyPlanReportWithDetails: Complete daily plan with subagent details
    """
    model_id = model_id or _default_model_id

    logger.info(f"Starting daily planner workflow with preferences: {user_preferences}")

    # Step 1: Analyze outdoor activities
    outdoor_analysis = analyze_outdoor_activities(
        user_preferences=user_preferences,
        model_id=model_id,
    )
    logger.info(f"Outdoor activity analysis complete")

    # Step 2: Analyze indoor activities
    indoor_analysis = analyze_indoor_activities(
        user_preferences=user_preferences,
        model_id=model_id,
    )
    logger.info(f"Indoor activity analysis complete")

    # Step 3: Aggregate results from both subagents
    aggregated_plan: DailyPlanReport = aggregate_daily_plan(
        outdoor_activity_analysis=outdoor_analysis,
        indoor_activity_analysis=indoor_analysis,
    )

    # Step 4: Create detailed report with subagent outputs
    detailed_plan = DailyPlanReportWithDetails(
        detailed_plan=aggregated_plan.detailed_plan,
        summary=aggregated_plan.summary,
        priority_score=aggregated_plan.priority_score,
        outdoor_activity_analysis=outdoor_analysis,
        indoor_activity_analysis=indoor_analysis,
    )

    logger.info("Daily planning workflow complete")
    return detailed_plan


# ============================================================================
# MAIN ENTRY POINT
# ============================================================================


def main():
    """
    Main entry point for the demo workflow.
    """
    # Set up OpenTelemetry and Braintrust logging
    logger.info("Setting up OpenTelemetry and Braintrust integration...")
    opentelemetry_init(console=True)

    # Enable OpenTelemetry callbacks for LiteLLM
    import litellm

    litellm.callbacks = ["otel"]

    # Example user input
    user_preferences = "User want to get more exercise and take a few days off from work"

    logger.info("=" * 80)
    logger.info("DEMO: Personal Daily Planner - Strands Agent Workflow")
    logger.info("=" * 80)
    logger.info(f"\nUser Preferences: {user_preferences}\n")

    # Run the daily planner workflow
    result = run_daily_planner(user_preferences=user_preferences)

    # Display results
    logger.info("=" * 80)
    logger.info("DAILY PLAN SUMMARY")
    logger.info("=" * 80)
    logger.info(f"\n{result.summary}\n")

    logger.info("=" * 80)
    logger.info("DETAILED DAILY PLAN")
    logger.info("=" * 80)
    logger.info(f"\n{result.detailed_plan}\n")

    logger.info("=" * 80)
    logger.info(f"Priority Score: {result.priority_score}/10")
    logger.info("=" * 80)

    # Display subagent details
    logger.info("\n" + "=" * 80)
    logger.info("SUBAGENT ANALYSIS DETAILS")
    logger.info("=" * 80)

    logger.info("\n--- Outdoor Activity Analysis ---")
    logger.info(result.outdoor_activity_analysis)

    logger.info("\n--- Indoor Activity Analysis ---")
    logger.info(result.indoor_activity_analysis)

    logger.info("\n" + "=" * 80)
    logger.info("Demo workflow completed successfully!")
    logger.info("Check Braintrust for detailed traces and logs.")
    logger.info("=" * 80)


if __name__ == "__main__":
    main()
