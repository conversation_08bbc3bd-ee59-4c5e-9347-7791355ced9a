import requests
import json


def get_experiment_list(project_name,token):
    url = f"https://api.braintrust.dev/v1/experiment?project_name={project_name}"
    headers = {
        "Authorization": f"Bearer {token}"
    }

    response = requests.get(url, headers=headers)

    if response.ok:
        data = response.json()
        experiment_list = []
        for experiment in data['objects']:
            experiment_list.append(experiment['id'])
    else:
        print("Request failed:", response.status_code)
        print(response.text)
    
    return experiment_list



experiments = get_experiment_list('pedro-project1','sk-in91pI0YSJ2ydhPAVvuJ16U33MlcE6gl0HxT5DI5tk2wsIoU')
for i in experiments:
    print(i)

