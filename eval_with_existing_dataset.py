#!/usr/bin/env python3
"""
Example showing how to use an existing Braintrust dataset in an evaluation.

This demonstrates the complete workflow:
1. Fetch an existing dataset
2. Use it in an evaluation with a custom task function
3. Apply scoring functions to evaluate performance

Usage:
    python eval_with_existing_dataset.py
    
Or run with braintrust CLI:
    BRAINTRUST_API_KEY=your_key braintrust eval eval_with_existing_dataset.py
"""

import braintrust
from braintrust import Eval

# Configuration
PROJECT_NAME = "your-project-name"
DATASET_NAME = "your-dataset-name"

def simple_task_function(input_text):
    """
    Example task function - replace this with your actual model/function.
    
    This is a simple example that just echoes the input with a prefix.
    In practice, this would be your LLM call, API request, or model inference.
    """
    return f"Processed: {input_text}"

def exact_match_scorer(input, output, expected):
    """
    Simple scoring function that checks for exact matches.
    
    Returns 1.0 for exact match, 0.0 otherwise.
    """
    return 1.0 if output == expected else 0.0

def contains_scorer(input, output, expected):
    """
    Scoring function that checks if the expected text is contained in the output.
    
    Returns 1.0 if expected is found in output, 0.0 otherwise.
    """
    if expected and output:
        return 1.0 if str(expected).lower() in str(output).lower() else 0.0
    return 0.0

def main():
    """
    Main function that fetches a dataset and runs an evaluation.
    """
    
    # Method 1: Fetch dataset first, then use in evaluation
    print("Fetching existing dataset...")
    dataset = braintrust.init_dataset(
        project=PROJECT_NAME,
        name=DATASET_NAME
    )
    
    print(f"Dataset '{dataset.name}' loaded from project '{dataset.project.name}'")
    
    # Run evaluation using the fetched dataset
    print("Running evaluation...")
    
    result = Eval(
        name="Evaluation with Existing Dataset",
        data=dataset,  # Use the fetched dataset
        task=simple_task_function,  # Your task function
        scores=[
            exact_match_scorer,
            contains_scorer,
        ]
    )
    
    print("Evaluation completed!")
    print(f"Results: {result}")

def alternative_approach():
    """
    Alternative approach: Pass dataset parameters directly to Eval.
    
    This is more concise but gives you less control over the dataset object.
    """
    
    # Method 2: Let Eval fetch the dataset internally
    result = Eval(
        name="Direct Dataset Evaluation",
        data=lambda: braintrust.init_dataset(
            project=PROJECT_NAME,
            name=DATASET_NAME
        ),
        task=simple_task_function,
        scores=[exact_match_scorer]
    )
    
    return result

if __name__ == "__main__":
    main()
    
    # Uncomment to try the alternative approach
    # print("\nTrying alternative approach...")
    # alternative_result = alternative_approach()
    # print(f"Alternative result: {alternative_result}")
