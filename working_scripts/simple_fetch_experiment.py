#!/usr/bin/env python3
"""
Simple example of fetching an existing experiment using Braintrust SDK.

Usage:
    python simple_fetch_experiment.py

Make sure to:
1. Install braintrust: pip install braintrust
2. Set your BRAINTRUST_API_KEY environment variable
3. Update PROJECT_NAME and EXPERIMENT_NAME below
"""

import braintrust

# Configuration - update these with your actual project and experiment names
PROJECT_NAME = "pedro-project1"
EXPERIMENT_NAME = "prompt1"

def main():
    try:
        # Fetch existing experiment (read-only)
        experiment = braintrust.init(
            project=PROJECT_NAME,
            experiment=EXPERIMENT_NAME,
            open=True  # This makes it read-only and fetches existing experiment
        )

        # Print basic experiment info
        print(f"Experiment: {EXPERIMENT_NAME}")
        print(f"Experiment ID: {experiment.id}")

        # Try to get the actual experiment name from metadata if available
        try:
            # Access the lazy metadata to get the actual experiment name
            metadata = experiment._lazy_metadata.get()
            actual_name = metadata.experiment.name
            print(f"Actual experiment name: {actual_name}")
        except AttributeError:
            print("(Using provided experiment name)")

        # Iterate through experiment records
        print("\nExperiment records:")
        record_count = 0
        for i, record in enumerate(experiment.fetch()):
            record_count += 1
            print(f"Record {i+1}:")
            print(f"  Input: {record.get('input')}")
            print(f"  Output: {record.get('output')}")
            print(f"  Expected: {record.get('expected')}")
            print(f"  Scores: {record.get('scores')}")
            print()

            # Stop after first 3 records for demo
            if i >= 2:
                break

        if record_count == 0:
            print("No records found in the experiment.")
        else:
            print(f"Experiment fetched successfully! Showed {min(record_count, 3)} records.")

    except ValueError as e:
        if "not found" in str(e):
            print(f"Error: {e}")
            print("Make sure the project and experiment names are correct.")
        else:
            raise
    except Exception as e:
        print(f"Error fetching experiment: {e}")
        print("Make sure your BRAINTRUST_API_KEY is set and valid.")

if __name__ == "__main__":
    main()
