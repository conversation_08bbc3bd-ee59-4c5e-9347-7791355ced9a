"""
<PERSON><PERSON><PERSON> to fetch an existing dataset named "test123" from Braintrust
"""

import braintrust
from braintrust import <PERSON>l<PERSON><PERSON>, Eva<PERSON>, traced, current_span,init_logger
from typing import List, Dict, Any
from autoevals import Humor
logger = init_logger(project="pedro-project1")

def fetch_dataset_test123(project_name, dataset_name) -> None:
    """
    Fetch and display records from the existing dataset "test123"
    
    Args:
        project_name: The project name where the dataset exists
    """
    try:
        print(f"Fetching dataset 'test123' from project '{project_name}'...")
        
        # Initialize connection to existing dataset
        dataset = braintrust.init_dataset(project=project_name, name=dataset_name)
        records = []
        for record in dataset:
            records.append(record)

        return records
        
    except Exception as e:
        print(f"Error fetching dataset: {e}")
        print("\nPossible issues:")
        print("1. Dataset 'test123' doesn't exist")
        print("2. Project name is incorrect")
        print("3. You don't have access to this dataset")
        print("4. API key is not set or invalid")
        return []
    
def filter_records_by_tag(records: List[Dict[str, Any]], target_tag: str) -> List[Dict[str, Any]]:
    """
    Filter records that contain a specific tag

    Args:
        records: List of dataset records
        target_tag: The tag to filter by

    Returns:
        List of records that contain the target tag
    """
    filtered_records = []

    for record in records:
        tags = record.get('tags', [])
        # Check if tags exist and if target_tag is in the list of tags
        if tags and target_tag in tags:
            filtered_records.append(record)

    return filtered_records

def create_dataset_from_records(
    records: List[Dict[str, Any]],
    project_name: str,
    new_dataset_name: str,
    description: str = None
) -> None:
    """
    Create a new dataset from a list of records

    Args:
        records: List of dataset records to insert
        project_name: Project name for the new dataset
        new_dataset_name: Name for the new dataset
        description: Optional description for the dataset
    """
    try:
        # Initialize new dataset
        new_dataset = braintrust.init_dataset(project=project_name, name=new_dataset_name)

        # Insert each record
        inserted_count = 0
        for record in records:
            try:
                # Insert record with all its fields
                record_id = new_dataset.insert(
                    input=record.get('input'),
                    expected=record.get('expected') or record.get('output'),  # Handle both field names
                    metadata=record.get('metadata'),
                    tags=record.get('tags')
                )
                inserted_count += 1
                print(f"Inserted record {inserted_count} with ID: {record_id}")

            except Exception as e:
                print(f"Error inserting record {inserted_count + 1}: {e}")

        # Flush to ensure all records are saved
        new_dataset.flush()

        print(f"\nSuccessfully created dataset '{new_dataset_name}' with {inserted_count} records")

        return new_dataset

    except Exception as e:
        print(f"Error creating dataset: {e}")
        return None
    
def convert_to_evalcases(records: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Convert dataset records to EvalCase format for evaluation

    Args:
        records: List of dataset records

    Returns:
        List of records in EvalCase format
    """
    eval_cases = []
    for record in records:
        # Extract the core fields from the record
        eval_case = {
            "input": record.get("input", ""),
            "expected": record.get("expected", record.get("output", "")),
            "metadata": record.get("metadata", {}),
            "tags": record.get("tags", [])
        }
        eval_cases.append(eval_case)

    return eval_cases

@traced 
def run_eval(project,data):
    Eval(
        project,  # Replace with your project name
        data=data,  # Replace with your eval dataset
        task=lambda input: "Generate a joke. Make it sound like a theme of " + input,  # Replace with your LLM call
        scores=[Humor],
        experiment_name='test-123'
    )


if __name__ == "__main__":    
    # You can change the project name if needed
    PROJECT_NAME = "pedro-project1"  # Change this if your dataset is in a different project
    dataset_name = "themes"
    tag = 'hello!'
    
    # Fetch the dataset
    records = fetch_dataset_test123(PROJECT_NAME,dataset_name)

    filtered_records = filter_records_by_tag(records, tag)
    eval_ready_records = convert_to_evalcases(filtered_records)

    #if you want to create a new dataset from the filtered one
    # create_dataset_from_records(filtered_records, PROJECT_NAME, "filtered_by_tag_themese")
    run_eval(PROJECT_NAME,eval_ready_records)