from autoevals.llm import LL<PERSON>lass<PERSON><PERSON>romTemplate
from openai import OpenAI
from autoevals import init

# Initialize with your OpenAI client
init(OpenAI())

# Create a classifier to evaluate if text is positive or negative
sentiment_classifier = LLMClassifierFromTemplate(
    name="sentiment",
    prompt_template="""
Analyze the sentiment of the following text and classify it as either positive or negative.

Text: {{output}}

Consider the overall tone, emotion, and context when making your decision.
""",
    choice_scores={"positive": 1, "negative": 0},
    use_cot=True,  # Enable chain-of-thought reasoning
    model="gpt-4o"
)

# Test the classifier
result = sentiment_classifier.eval(
    output="I absolutely love this new feature! It makes my work so much easier."
)

print(f"Score: {result.score}")  # 1 for positive, 0 for negative
print(f"Choice: {result.metadata['choice']}")  # "positive" or "negative"
print(f"Reasoning: {result.metadata['rationale']}")  # Chain-of-thought explanation