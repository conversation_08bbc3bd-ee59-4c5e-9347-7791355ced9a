import braintrust
dataset = braintrust.init_dataset(project="pedro-project1", name="themes", _internal_btql={"limit": 1})
records = []
for record in dataset:
    records.append(record)

for i in records:
    print(i)


# import braintrust
# dataset = braintrust.init_dataset(project="pedro-repro5374", name="Dataset 1")
# dataset2 = braintrust.init_dataset(project="pedro-repro5374", name="Dataset 2")

# for record in dataset:
#         dataset2.insert(
#             input=record['input'], expected=record['expected'], metadata=record['metadata'], tags=record['tags']
#         )