import os
from openai import OpenAI

client = OpenAI(
    base_url="https://api.braintrust.dev/v1/proxy",
    api_key=os.environ["BRAINTRUST_API_KEY"],
)

response = client.chat.completions.create(
    model="sonar-pro",
    messages=[{"role": "user", "content": "What's the latest news about AI?"}],
    extra_body={
        "search_domain_filter": ["cnn.com", "bbc.com", "reuters.com"],
        "search_recency_filter": "day",
        "return_images": False,
        "return_related_questions": True,
    }
)
print(response)