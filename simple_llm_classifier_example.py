#!/usr/bin/env python3
"""
Simple, working example of LLMClassifier concepts in Braintrust.

This example demonstrates LLM-based classification using a straightforward approach
that works reliably with the Braintrust Eval function.
"""

import os
from braintrust import Eval, init_logger
from openai import OpenAI

# Initialize Braintrust logger
init_logger(project="simple-llm-classifier")

# Initialize OpenAI client
openai_client = OpenAI()

def llm_sentiment_scorer(output, expected=None, input=None, **kwargs):
    """
    LLM-based sentiment classifier scorer.
    This function demonstrates the core LLMClassifier concept.
    """
    
    # Construct the classification prompt
    prompt = f"""You are a sentiment analysis expert. Analyze the given text and classify its sentiment.

Text to analyze: {output}

Think step by step about the emotional tone, word choice, and overall sentiment expressed in the text.

Based on your analysis, classify the sentiment as exactly one of:
- Positive
- Negative  
- Neutral

Provide only your final classification (one word):"""

    try:
        # Call the LLM for classification
        response = openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[{"role": "user", "content": prompt}],
            temperature=0.1,  # Low temperature for consistent results
            max_tokens=10     # We only need one word
        )
        
        classification = response.choices[0].message.content.strip()
        
        # Map classification to scores (this is the key LLMClassifier concept)
        choice_scores = {
            "Positive": 1.0,
            "Negative": 0.0,
            "Neutral": 0.5
        }
        
        # Get score, default to neutral if classification not recognized
        score = choice_scores.get(classification, 0.5)
        
        return {
            "name": "llm_sentiment_classifier",
            "score": score,
            "metadata": {
                "classification": classification,
                "expected": expected,
                "model": "gpt-4o-mini"
            }
        }
        
    except Exception as e:
        print(f"Error in LLM classification: {e}")
        return {
            "name": "llm_sentiment_classifier",
            "score": 0.5,  # Default neutral score on error
            "metadata": {
                "classification": "Error",
                "error": str(e)
            }
        }

def llm_quality_scorer(output, expected=None, input=None, **kwargs):
    """
    LLM-based quality assessment scorer.
    Evaluates response quality using multiple criteria.
    """
    
    # Construct the quality evaluation prompt
    prompt = f"""You are an expert evaluator of text quality. Evaluate the following text:

Text to evaluate: {output}

Consider these criteria:
1. Clarity: Is it easy to understand?
2. Completeness: Does it provide sufficient information?
3. Accuracy: Does it seem factually correct?
4. Helpfulness: Would this be useful to someone?

Think through each criterion, then classify the overall quality as exactly one of:
- Excellent
- Good
- Fair
- Poor

Provide only your final classification (one word):"""

    try:
        # Call the LLM for quality assessment
        response = openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[{"role": "user", "content": prompt}],
            temperature=0.1,
            max_tokens=10
        )
        
        classification = response.choices[0].message.content.strip()
        
        # Map quality classifications to scores
        choice_scores = {
            "Excellent": 1.0,
            "Good": 0.75,
            "Fair": 0.5,
            "Poor": 0.0
        }
        
        score = choice_scores.get(classification, 0.5)
        
        return {
            "name": "llm_quality_classifier",
            "score": score,
            "metadata": {
                "classification": classification,
                "expected": expected,
                "model": "gpt-4o-mini"
            }
        }
        
    except Exception as e:
        print(f"Error in LLM quality assessment: {e}")
        return {
            "name": "llm_quality_classifier",
            "score": 0.5,
            "metadata": {
                "classification": "Error",
                "error": str(e)
            }
        }

def run_sentiment_classification():
    """Run sentiment classification evaluation."""
    
    print("Running sentiment classification...")
    
    # Test data - each item has input text and expected sentiment
    test_data = [
        {
            "input": "I absolutely loved it! The service was fantastic and exceeded all my expectations.",
            "expected": "Positive"
        },
        {
            "input": "It was terrible. The plot made no sense and the acting was awful.",
            "expected": "Negative"
        },
        {
            "input": "It's 72 degrees and partly cloudy.",
            "expected": "Neutral"
        },
        {
            "input": "I'm so frustrated! Everything went wrong and nothing worked as planned.",
            "expected": "Negative"
        }
    ]
    
    # Run the evaluation
    result = Eval(
        "Sentiment Classification with LLM",
        data=lambda: test_data,
        task=lambda input_text: input_text,  # Pass through the text to be classified
        scores=[llm_sentiment_scorer]
    )
    
    return result

def run_quality_assessment():
    """Run quality assessment evaluation."""
    
    print("Running quality assessment...")
    
    # Test data with responses of varying quality
    test_data = [
        {
            "input": "The capital of France is Paris. It's located in the north-central part of the country and is known for landmarks like the Eiffel Tower and Louvre Museum.",
            "expected": "Excellent"
        },
        {
            "input": "Mix ingredients and bake.",
            "expected": "Poor"
        },
        {
            "input": "Rain is caused by the water cycle. When water evaporates, it rises and forms clouds, then falls as precipitation.",
            "expected": "Good"
        },
        {
            "input": "Yes.",
            "expected": "Poor"
        }
    ]
    
    # Run the evaluation
    result = Eval(
        "Quality Assessment with LLM",
        data=lambda: test_data,
        task=lambda input_text: input_text,  # Pass through the text to be assessed
        scores=[llm_quality_scorer]
    )
    
    return result

def main():
    """Main function to run LLM classification examples."""
    
    # Check for required API keys
    if not os.getenv("BRAINTRUST_API_KEY"):
        print("❌ BRAINTRUST_API_KEY environment variable not set.")
        print("Set it with: export BRAINTRUST_API_KEY=<your-api-key>")
        return
    
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ OPENAI_API_KEY environment variable not set.")
        print("Set it with: export OPENAI_API_KEY=<your-openai-api-key>")
        return
    
    print("🚀 Simple LLM Classifier Examples")
    print("=" * 50)
    print("This demonstrates LLMClassifier concepts with actual LLM calls!")
    
    try:
        # Run sentiment classification
        print("\n1. Sentiment Classification:")
        sentiment_result = run_sentiment_classification()
        print("✅ Sentiment classification completed!")
        
        # Run quality assessment  
        print("\n2. Quality Assessment:")
        quality_result = run_quality_assessment()
        print("✅ Quality assessment completed!")
        
        print("\n🎉 All evaluations completed successfully!")
        print("\n📊 Results:")
        print("- Check the Braintrust dashboard for detailed results")
        print("- Each classification includes the LLM's reasoning")
        print("- Scores are mapped from classifications to numerical values")
        
        print("\n💡 Key LLMClassifier Concepts Demonstrated:")
        print("- LLM-based classification with structured prompts")
        print("- Choice-to-score mapping (Positive=1.0, Negative=0.0, etc.)")
        print("- Error handling and fallback scores")
        print("- Integration with Braintrust evaluation framework")
        
    except Exception as e:
        print(f"❌ Error running evaluations: {e}")
        print("Make sure your API keys are set correctly and you have internet access.")

if __name__ == "__main__":
    main()
