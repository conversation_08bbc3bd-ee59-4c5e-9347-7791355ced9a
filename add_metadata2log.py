bt_api_key='sk-in91pI0YSJ2ydhPAVvuJ16U33MlcE6gl0HxT5DI5tk2wsIoU'
twlabs_api_key='tlk_2X6XARP1DDRGS022R26W22TMBQFC'

BRAINTRUST_API_KEY = bt_api_key

from braintrust import init_logger, traced
logger = init_logger(project="pedro-multimodal-mind2web-eval")
logger = init_logger(project="pedro-multimodal-mind2web-eval")

#This adds a random log that does not seem included in any span:
# Later in your code, you can log with metadata:
logger.log(
    input="Your input data",
    output="Your output data", 
    metadata={
        "user_id": "pedro",
        "experiment_version": "v1.0",
        "model_config": {"temperature": 0.2, "max_tokens": 150}
    }
)

import os
import json
import base64
import re
import time
from typing import Dict, Any, List, Optional, Tuple
 
from lxml import etree
import openai
from datasets import load_dataset
from PIL import Image
from io import BytesIO
 
from braintrust import (
    Eval,
    Attachment,
    start_span,
    wrap_openai,
)
 
# Constants
MAX_SAMPLES = 50
HTML_MAX_ELEMENTS = 50
MAX_PREVIOUS_ACTIONS = 3
 
# Uncomment the following line to hardcode your API key
# os.environ["BRAINTRUST_API_KEY"] = "YOUR_API_KEY_HERE"
 
client = wrap_openai(
    openai.OpenAI(
        api_key=BRAINTRUST_API_KEY,
        base_url="https://api.braintrust.dev/v1/proxy",
    )
)

def process_screenshot(screenshot_input: Any) -> Optional[Attachment]:
    with start_span(name="process_screenshot") as span:
        #this adds metadata only to the process_screenshot span
        # Add metadata to the span
        span.log(metadata={
            "input_type": type(screenshot_input).__name__,
            "processing_stage": "screenshot_conversion",
            "timestamp": time.time()
        })
        try:
            # Handle PIL Image
            if isinstance(screenshot_input, Image.Image):
                img_byte_arr = BytesIO()
                screenshot_input.save(img_byte_arr, format="PNG")
                image_data = img_byte_arr.getvalue()
 
            # Handle file path
            elif isinstance(screenshot_input, str) and os.path.exists(screenshot_input):
                with open(screenshot_input, "rb") as f:
                    image_data = f.read()
 
            # Handle bytes
            elif isinstance(screenshot_input, bytes):
                image_data = screenshot_input
 
            # Handle dictionary with base64 data
            elif isinstance(screenshot_input, dict) and "data" in screenshot_input:
                data = screenshot_input["data"]
                if not isinstance(data, str):
                    return None
 
                # Process base64 data
                if data.startswith("data:image"):
                    base64_data = data.split(",", 1)[1]
                elif data.startswith("/9j/") or data.startswith("iVBOR"):
                    base64_data = data
                else:
                    return None
 
                image_data = base64.b64decode(base64_data)
            else:
                return None
 
            # Create attachment
            result = Attachment(
                data=image_data,
                filename="screenshot.png",
                content_type="image/png",
            )
 
            return result
 
        except Exception:
            return None
        
def get_enhanced_tree_summary(
    html_content: str, max_items: int = HTML_MAX_ELEMENTS
) -> str:
    with start_span(name="html_parsing") as span:
        if not html_content:
            return "No HTML content provided"
 
        try:
            # Parse HTML
            parser = etree.HTMLParser()
            dom_tree = etree.fromstring(html_content, parser)
 
            # XPath for interactive elements, sorted by relevance
            xpath_queries = [
                "//button | //input[@type='submit'] | //input[@type='button']",
                "//a[@href] | //*[@role='button'] | //*[@onclick]",
                "//input[not(@type='hidden')] | //select | //textarea",
                "//label | //form",
                "//h1 | //h2 | //h3 | //nav | //*[@role='navigation']",
            ]
 
            # Collect elements by priority until max_items is reached
            important_elements = []
            for query in xpath_queries:
                if len(important_elements) >= max_items:
                    break
                elements = dom_tree.xpath(query)
                remaining_slots = max_items - len(important_elements)
                important_elements.extend(elements[:remaining_slots])
 
            # Create a concise representation
            summary = []
            for elem in important_elements:
                tag = elem.tag
 
                # Get text content, limited to 30 chars
                text = elem.text.strip() if elem.text else ""
                if not text:
                    for child in elem.xpath(".//text()"):
                        if child.strip():
                            text += " " + child.strip()
                text = text.strip()[:30]
 
                # Get key attributes
                key_attrs = [
                    "id",
                    "type",
                    "placeholder",
                    "href",
                    "role",
                    "aria-label",
                    "value",
                    "name",
                ]
                attrs = []
                for k in key_attrs:
                    if k in elem.attrib:
                        attrs.append(f'{k}="{elem.attrib[k]}"')
 
                # Format element representation
                elem_repr = f"<{tag} {' '.join(attrs)}>{text}</{tag}>"
                summary.append(elem_repr)
 
            return "\n".join(summary)
 
        except Exception as e:
            return f"Error parsing HTML: {str(e)}"
        
def format_previous_actions(
    actions: List[str], max_actions: int = MAX_PREVIOUS_ACTIONS
) -> str:
    if not actions:
        return "None"
 
    # Only take the most recent actions
    recent_actions = actions[-max_actions:]
 
    # Format with numbering
    formatted = "\n".join(
        [f"{i+1}. {action}" for i, action in enumerate(recent_actions)]
    )
 
    # Indicate if there were more actions before these
    if len(actions) > max_actions:
        formatted = (
            f"Showing {max_actions} most recent of {len(actions)} total actions\n"
            + formatted
        )
 
    return formatted

def parse_operation_string(operation_str: str) -> Dict[str, str]:
    with start_span(name="parse_operation") as span:
        # Default values
        operation = {"op": "CLICK", "value": ""}
 
        if not operation_str:
            return operation
 
        try:
            # Try parsing as JSON first
            if operation_str.strip().startswith("{"):
                parsed = json.loads(operation_str)
                if isinstance(parsed, dict):
                    operation["op"] = parsed.get("op", "CLICK")
                    operation["value"] = parsed.get("value", "")
            else:
                # Fallback to regex parsing
                import re
 
                match_op = re.search(r"(CLICK|TYPE|SELECT)", operation_str)
                if match_op:
                    operation["op"] = match_op.group(1)
                    match_value = re.search(
                        r'value\s*[:=]\s*["\']?([^"\']+)["\']?', operation_str
                    )
                    if match_value:
                        operation["value"] = match_value.group(1)
        except Exception:
            pass
 
        return operation
    
def load_mind2web_samples(
    max_samples: int = MAX_SAMPLES, use_smaller_subset: bool = True
) -> List[Dict[str, Any]]:
 
    # Load the dataset with streaming to conserve memory
    split = "test_domain" if use_smaller_subset else "train"
    dataset = load_dataset("osunlp/Multimodal-Mind2Web", split=split, streaming=True)
 
    processed_samples = []
    successful_samples = 0
 
    # Process samples
    for item in dataset:
        if successful_samples >= max_samples:
            break
 
        try:
            with start_span(name="process_sample") as sample_span:
                # Extract basic fields
                annotation_id = item.get(
                    "annotation_id", f"sample_{successful_samples}"
                )
                website = item.get("website", "unknown")
                confirmed_task = item.get("confirmed_task", "Navigate the website")
                cleaned_html = item.get("cleaned_html", "<html></html>")
                operation_str = item.get("operation", '{"op": "CLICK", "value": ""}')
 
                # Process operation
                operation = parse_operation_string(operation_str)
 
                # Process screenshot
                screenshot_attachment = None
                screenshot_dict = item.get("screenshot")
                if screenshot_dict:
                    screenshot_attachment = process_screenshot(screenshot_dict)
 
                # Process HTML summary
                html_summary = get_enhanced_tree_summary(
                    cleaned_html, max_items=HTML_MAX_ELEMENTS
                )
 
                # Process previous actions
                action_reprs = item.get("action_reprs", [])
                previous_actions_str = format_previous_actions(
                    action_reprs, max_actions=MAX_PREVIOUS_ACTIONS
                )
 
                # Map operation type to the correct option letter
                expected_option = "A"  # Default to CLICK
                if operation["op"] == "TYPE":
                    expected_option = "B"
                elif operation["op"] == "SELECT":
                    expected_option = "C"
 
                # Create a focused prompt
                formatted_prompt = f"""
                    Task: {confirmed_task}
 
                    Key webpage elements:
                    {html_summary}
 
                    Previous actions:
                    {previous_actions_str}
 
                    What should be the next action? Select from:
                    A. Click the appropriate element based on the task
                    B. Type text into an input field
                    C. Select an option from a dropdown
                    """
 
                # Build complete sample
                sample = {
                    "annotation_id": annotation_id,
                    "website": website,
                    "confirmed_task": confirmed_task,
                    "html_summary": html_summary,
                    "operation": operation,
                    "previous_actions_str": previous_actions_str,
                    "formatted_prompt": formatted_prompt,
                    "expected_option": expected_option,
                    "expected_action": operation["op"],
                    "expected_value": operation["value"],
                    "screenshot_attachment": screenshot_attachment,
                }
 
                processed_samples.append(sample)
                successful_samples += 1
 
        except Exception:
            continue
 
    return processed_samples

def create_braintrust_dataset(samples: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
 
    dataset_samples = []
 
    for sample in samples:
        if not isinstance(sample, dict):
            continue
 
        # Extract operation details
        operation = sample.get("operation", {})
        operation_type = (
            operation.get("op", "CLICK") if isinstance(operation, dict) else "CLICK"
        )
        operation_value = (
            operation.get("value", "") if isinstance(operation, dict) else ""
        )
 
        # Create dataset entry
        dataset_entry = {
            "input": {
                "prompt": sample.get("formatted_prompt", ""),
                "task": sample.get("confirmed_task", ""),
                "website": sample.get("website", ""),
                "previous_actions": sample.get("previous_actions_str", "None"),
            },
            "expected": {
                "option": sample.get("expected_option", ""),
                "action": operation_type,
                "value": operation_value,
            },
            "metadata": {
                "annotation_id": sample.get("annotation_id", ""),
                "website": sample.get("website", ""),
                "operation_type": operation_type,
            },
        }
 
        # Add screenshot attachment if available
        if sample.get("screenshot_attachment"):
            dataset_entry["input"]["screenshot"] = sample["screenshot_attachment"]
 
        dataset_samples.append(dataset_entry)
 
    return dataset_samples

def predict_with_gpt4o(input_data: Dict[str, Any]) -> Dict[str, Any]:
    with start_span(name="model_prediction") as predict_span:
        # Add metadata to the prediction span
        predict_span.log(metadata={
            "website": input_data.get("website", "unknown"),
            "task_type": input_data.get("task", ""),
            "has_screenshot": "screenshot" in input_data,
            "prompt_length": len(input_data.get("prompt", "")),
        })
        try:
            # Extract input components
            prompt = input_data.get("prompt", "")
            screenshot_attachment = input_data.get("screenshot")
 
            # Create system message requesting JSON output
            system_message = """You are a web navigation assistant that helps users complete tasks online.
                Analyze the webpage and determine the best action to take next based on the task.
 
                You MUST respond with a valid JSON object with the following structure:
                {
                "option": "A, B, or C",
                "op": "CLICK, TYPE, or SELECT",
                "value": "Only provide value for TYPE/SELECT actions"
                }
 
                Option A corresponds to CLICK, B to TYPE, and C to SELECT.
                For CLICK operations, include an empty value field.
 
                Example for clicking:
                {"option": "A", "op": "CLICK", "value": ""}
 
                Example for typing:
                {"option": "B", "op": "TYPE", "value": "search query text"}
 
                Example for selecting:
                {"option": "C", "op": "SELECT", "value": "dropdown option"}
                """
 
            # Create messages array
            messages = [{"role": "system", "content": system_message}]
 
            # Add screenshot if available
            if screenshot_attachment and hasattr(screenshot_attachment, "data"):
                try:
                    image_data = screenshot_attachment.data
                    base64_image = base64.b64encode(image_data).decode("utf-8")
 
                    messages.append(
                        {
                            "role": "user",
                            "content": [
                                {
                                    "type": "image_url",
                                    "image_url": {
                                        "url": f"data:image/png;base64,{base64_image}"
                                    },
                                },
                                {"type": "text", "text": prompt},
                            ],
                        }
                    )
                except Exception:
                    messages.append({"role": "user", "content": prompt})
            else:
                messages.append({"role": "user", "content": prompt})
 
            # Request JSON output format
            response = client.chat.completions.create(
                model="gpt-4o",
                messages=messages,
                max_tokens=150,
                temperature=0.2,
                response_format={"type": "json_object"},  # This is critical!
            )
 
            result = response.choices[0].message.content
 
            # Parse JSON response
            try:
                structured_response = json.loads(result)
 
                # Ensure the required fields exist
                if "option" not in structured_response:
                    structured_response["option"] = ""
                if "op" not in structured_response:
                    structured_response["op"] = ""
                if "value" not in structured_response:
                    structured_response["value"] = ""
 
                return structured_response
 
            except json.JSONDecodeError as e:
                # If JSON parsing fails, try to extract data from text
                option_match = re.search(r"Answer:\s*([ABC])", result, re.IGNORECASE)
                action_match = re.search(
                    r"Action:\s*(CLICK|TYPE|SELECT)", result, re.IGNORECASE
                )
                value_match = re.search(r"Value:\s*(.+?)(?:\n|$)", result)
 
                option = option_match.group(1).upper() if option_match else ""
                action = action_match.group(1).upper() if action_match else ""
                value = value_match.group(1).strip() if value_match else ""
 
                # Convert to structured format
                return {
                    "option": option,
                    "op": action,
                    "value": value,
                    "error": f"JSON parsing failed: {str(e)}",
                }
 
        except Exception as e:
            # Return error information in JSON format
            return {"option": "", "op": "ERROR", "value": str(e), "error": str(e)}
        
def option_selection_scorer(output: Dict[str, str], expected: Dict[str, Any]) -> int:
    return int(output["op"] == expected["action"])

def action_correctness_scorer(output: Dict[str, str], expected: Dict[str, Any]) -> int:
    # First, check if both action types match (note output uses "op" key)
    action_matches = output["op"] == expected["action"]
 
    # If the actions don't match, return 0 immediately
    if not action_matches:
        return 0
 
    # If we're dealing with a CLICK action, we've already confirmed they match
    if expected["action"] == "CLICK":
        return 1
 
    # For TYPE or SELECT, check if values match too
    return int(output["value"] == expected["value"])

def run_mind2web_evaluation(sample_size: int = MAX_SAMPLES) -> None:
    try:
        # Load samples
        samples = load_mind2web_samples(max_samples=sample_size)
 
        if not samples:
            return
 
        # Create Braintrust dataset
        dataset = create_braintrust_dataset(samples)
 
        # Run the evaluation
        experiment_name = f"mind2web-{int(time.time())}"
        Eval(
            "pedro-multimodal-mind2web-eval",  # Project name
            data=dataset,
            task=predict_with_gpt4o,
            scores=[option_selection_scorer, action_correctness_scorer],
            experiment_name=experiment_name,
            metadata={
                "model": "gpt-4o",
            },
        )
 
    except Exception as e:
        print(f"Evaluation failed: {e}")
 
 
if __name__ == "__main__":
    # Run evaluation with a smaller sample size for testing. Adjust this number to run on more or less samples.
    run_mind2web_evaluation(sample_size=10)