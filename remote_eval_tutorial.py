from typing import List
 
import openai
from autoevals import <PERSON><PERSON><PERSON>ein
from braintrust import Eva<PERSON>, init_dataset, wrap_openai
from pydantic import BaseModel, Field
 
client = wrap_openai(openai.AsyncOpenAI())
 
 
# Define Pydantic models for validation (optional)
# These single-field models act as validators for individual parameters
class Person(BaseModel):
    name: str
    age: int
 
 
class BooleanParam(BaseModel):
    value: bool = Field(default=False, description="Include a contextual prefix")
 
 
class StringParam(BaseModel):
    value: str = Field(default="this is a math problem", description="The prefix to include")
 
 
class ArrayParam(BaseModel):
    value: List[Person] = Field(
        default=[
            Person(name="<PERSON>", age=30),
            Person(name="<PERSON>", age=25),
        ],
        description="List of people",
    )
 
 
async def task(input, hooks):
    parameters = hooks.parameters
 
    # Access parameters directly, matching TypeScript structure
    prefix = parameters.get("prefix", "this is a math problem")
    if parameters.get("include_prefix", False):
        prompt_input = f"{prefix}:{input}"
    else:
        prompt_input = input
 
    # Build and execute the prompt using the main prompt parameter
    completion = await client.chat.completions.create(**parameters["main"].build(input=prompt_input))
    return completion.choices[0].message.content or ""
 
 
Eval(
    "Simple eval",
    data=init_dataset("local dev", "sanity"),  # Datasets are currently ignored
    task=task,
    scores=[Levenshtein],  # These scores will be used along with any configured in the UI
    parameters={
        "main": {
            "type": "prompt",
            "description": "This is the main prompt",
            "default": {
                "prompt": {"type": "chat", "messages": [{"role": "user", "content": "{{input}}"}]},
                "options": {"model": "gpt-4o"},
            },
        },
        "another": {
            "type": "prompt",
            "description": "This is another prompt",
            "default": {
                "prompt": {"type": "chat", "messages": [{"role": "user", "content": "{{input}}"}]},
                "options": {"model": "gpt-4o"},
            },
        },
        # Optional: Add validators for individual parameters
        # When using single-field Pydantic models, the SDK automatically
        # unwraps the value, so you access parameters["prefix"] directly
        "include_prefix": BooleanParam,
        "prefix": StringParam,
        "array_of_objects": ArrayParam,
    },
)