#!/usr/bin/env python3
"""
Example of running an experiment with AI models using Braintrust SDK.

This example shows how to:
1. Run experiments with OpenAI models
2. Use Braintrust's model wrapping for automatic logging
3. Test different prompts and models
4. Compare results across experiments

Prerequisites:
- pip install braintrust openai
- Set BRAINTRUST_API_KEY environment variable
- Set OPENAI_API_KEY environment variable
"""

import braintrust
from openai import OpenAI
import time
from typing import List, Dict, Any


def setup_openai_client():
    """Set up OpenAI client with Braintrust wrapping for automatic logging."""
    client = OpenAI()
    # Wrap the client to automatically log API calls to Braintrust
    wrapped_client = braintrust.wrap_openai(client)
    return wrapped_client


def simple_qa_task(client, question: str, model: str = "gpt-3.5-turbo") -> str:
    """
    Simple Q&A task using OpenAI.
    
    The wrapped client will automatically log the API call details.
    """
    response = client.chat.completions.create(
        model=model,
        messages=[
            {"role": "system", "content": "You are a helpful assistant. Give concise, accurate answers."},
            {"role": "user", "content": question}
        ],
        temperature=0.1,
        max_tokens=100
    )
    
    return response.choices[0].message.content


def contains_keyword_score(input_question: str, output: str, expected_keyword: str) -> float:
    """
    Score based on whether the output contains expected keywords.
    """
    if not output or not expected_keyword:
        return 0.0
    
    return 1.0 if expected_keyword.lower() in output.lower() else 0.0


def length_appropriateness_score(input_question: str, output: str, expected: str = None) -> float:
    """
    Score based on whether the output length is appropriate (not too short, not too long).
    """
    if not output:
        return 0.0
    
    output_len = len(output.split())
    
    # Consider 5-50 words as appropriate length for most Q&A
    if 5 <= output_len <= 50:
        return 1.0
    elif output_len < 5:
        return 0.3  # Too short
    else:
        return 0.7  # Too long but still has content


def run_qa_experiment():
    """
    Run a Q&A experiment with OpenAI models.
    """
    print("Setting up OpenAI client...")
    client = setup_openai_client()
    
    print("Initializing experiment...")
    experiment = braintrust.init(
        project="ai-model-testing",
        experiment="openai-qa-experiment",
        description="Testing OpenAI models on Q&A tasks"
    )
    
    # Test questions with expected keywords to look for in answers
    test_questions = [
        {
            "question": "What is the capital of France?",
            "expected_keyword": "Paris",
            "category": "geography"
        },
        {
            "question": "Who wrote Romeo and Juliet?",
            "expected_keyword": "Shakespeare",
            "category": "literature"
        },
        {
            "question": "What is 2 + 2?",
            "expected_keyword": "4",
            "category": "math"
        },
        {
            "question": "What programming language is known for 'Hello, World!'?",
            "expected_keyword": "C",
            "category": "programming"
        },
        {
            "question": "What is the largest planet in our solar system?",
            "expected_keyword": "Jupiter",
            "category": "science"
        }
    ]
    
    print(f"Running {len(test_questions)} test questions...")
    
    for i, test_case in enumerate(test_questions):
        question = test_case["question"]
        expected_keyword = test_case["expected_keyword"]
        category = test_case["category"]
        
        print(f"  Question {i+1}: {question}")
        
        try:
            # Measure response time
            start_time = time.time()
            answer = simple_qa_task(client, question)
            response_time = time.time() - start_time
            
            # Calculate scores
            keyword_score = contains_keyword_score(question, answer, expected_keyword)
            length_score = length_appropriateness_score(question, answer)
            
            # Log to experiment
            experiment.log(
                input=question,
                output=answer,
                scores={
                    "contains_keyword": keyword_score,
                    "length_appropriate": length_score,
                    "overall": (keyword_score + length_score) / 2
                },
                metadata={
                    "expected_keyword": expected_keyword,
                    "category": category,
                    "response_time_seconds": response_time,
                    "model": "gpt-3.5-turbo",
                    "question_id": i
                },
                tags=["qa", category, "openai"]
            )
            
            print(f"    Answer: {answer}")
            print(f"    Keyword found: {'✓' if keyword_score > 0 else '✗'}")
            print(f"    Response time: {response_time:.2f}s")
            
        except Exception as e:
            print(f"    Error: {e}")
            # Log the error
            experiment.log(
                input=question,
                output=None,
                error=str(e),
                scores={"contains_keyword": 0.0, "length_appropriate": 0.0, "overall": 0.0},
                metadata={
                    "expected_keyword": expected_keyword,
                    "category": category,
                    "error_type": type(e).__name__,
                    "question_id": i
                },
                tags=["qa", category, "error"]
            )
    
    # Get experiment summary
    print("\nGetting experiment summary...")
    summary = experiment.summarize()
    
    print(f"\nExperiment Results:")
    print(f"  Project: {summary.project_name}")
    print(f"  Experiment: {summary.experiment_name}")
    print(f"  URL: {summary.experiment_url}")
    
    if summary.scores:
        print(f"  Average Scores:")
        for score in summary.scores:
            print(f"    {score.name}: {score.score:.3f}")
    
    return experiment, summary


def compare_models_experiment():
    """
    Run an experiment comparing different models.
    """
    print("\nRunning model comparison experiment...")
    
    client = setup_openai_client()
    
    experiment = braintrust.init(
        project="ai-model-testing",
        experiment="model-comparison",
        description="Comparing different OpenAI models"
    )
    
    # Test with different models
    models_to_test = ["gpt-3.5-turbo", "gpt-4o-mini"]
    test_question = "Explain quantum computing in simple terms."
    
    for model in models_to_test:
        print(f"  Testing model: {model}")
        
        try:
            start_time = time.time()
            answer = simple_qa_task(client, test_question, model=model)
            response_time = time.time() - start_time
            
            # Simple scoring based on answer length and response time
            length_score = length_appropriateness_score(test_question, answer)
            speed_score = 1.0 if response_time < 5.0 else 0.5  # Prefer faster responses
            
            experiment.log(
                input=test_question,
                output=answer,
                scores={
                    "length_appropriate": length_score,
                    "response_speed": speed_score,
                    "overall": (length_score + speed_score) / 2
                },
                metadata={
                    "model": model,
                    "response_time_seconds": response_time,
                    "answer_length_words": len(answer.split()) if answer else 0
                },
                tags=["model-comparison", "quantum-computing"]
            )
            
            print(f"    Response time: {response_time:.2f}s")
            print(f"    Answer length: {len(answer.split()) if answer else 0} words")
            
        except Exception as e:
            print(f"    Error with {model}: {e}")
            experiment.log(
                input=test_question,
                output=None,
                error=str(e),
                metadata={"model": model, "error_type": type(e).__name__},
                tags=["model-comparison", "error"]
            )
    
    summary = experiment.summarize()
    print(f"Model comparison completed: {summary.experiment_url}")
    
    return experiment, summary


def main():
    """
    Main function to run AI model experiments.
    """
    print("AI Model Experiment Examples")
    print("=" * 50)
    
    try:
        # Check if OpenAI API key is available
        import os
        if not os.getenv("OPENAI_API_KEY"):
            print("Warning: OPENAI_API_KEY not found. Set this environment variable to run OpenAI experiments.")
            print("You can still run the script to see the structure, but API calls will fail.")
        
        # Run Q&A experiment
        exp1, summary1 = run_qa_experiment()
        
        # Run model comparison experiment
        exp2, summary2 = compare_models_experiment()
        
        print("\n" + "=" * 50)
        print("All experiments completed!")
        print(f"Q&A Experiment: {summary1.experiment_url}")
        print(f"Model Comparison: {summary2.experiment_url}")
        
        # Flush experiments
        exp1.flush()
        exp2.flush()
        
    except Exception as e:
        print(f"Error running experiments: {e}")
        print("\nMake sure you have:")
        print("1. Set BRAINTRUST_API_KEY environment variable")
        print("2. Set OPENAI_API_KEY environment variable")
        print("3. Installed required packages: pip install braintrust openai")


if __name__ == "__main__":
    main()
