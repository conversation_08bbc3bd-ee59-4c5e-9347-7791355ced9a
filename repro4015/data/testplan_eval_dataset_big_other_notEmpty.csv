test_plan,prd_document,technical_document,test_format
"[
  {
    ""testSuite"": {
      ""name"": ""Oracle Test Suite"",
      ""description"": ""Functional test plan covering the Oracle Support Dashboard and Support Stats pages."",
      ""testCases"": [
        {
          ""title"": ""Verify navigation to Support Dashboard"",
          ""precondition"": [
            ""User is logged in"",
            ""User can access the Oracle app main page (reference UC-1)""
          ],
          ""steps"": [
            {
              ""action"": ""Click 'Dashboard' in the top navigation bar"",
              ""expected"": ""The app navigates to the Support Dashboard without a full page reload""
            },
            {
              ""action"": ""Observe the page header with the text 'Oracle'"",
              ""expected"": ""Header bar is visible with the H2 title displaying 'Oracle'""
            }
          ],
          ""finalExpected"": ""Support Dashboard is displayed with the correct layout and no reload""
        },
        {
          ""title"": ""Verify navigation to Support Stats page"",
          ""precondition"": [
            ""User is logged in"",
            ""User can access the Oracle app main page (reference UC-4)""
          ],
          ""steps"": [
            {
              ""action"": ""Click 'Stats' in the top navigation bar"",
              ""expected"": ""App navigates to the Support Stats page without a full reload""
            },
            {
              ""action"": ""Check the page heading 'Support Stats'"",
              ""expected"": ""Header bar shows 'Support Stats' in H2""
            }
          ],
          ""finalExpected"": ""Support Stats page is displayed, matching the layout described in PRD""
        },
        {
          ""title"": ""Verify search with debounced input on Support Dashboard"",
          ""precondition"": [
            ""User is on the Support Dashboard (reference UC-1)""
          ],
          ""steps"": [
            {
              ""action"": ""Type 'urgent' into the search box"",
              ""expected"": ""After 300ms delay, tickets list updates to show tickets matching 'urgent'""
            },
            {
              ""action"": ""Change input to 'critical'"",
              ""expected"": ""After 300ms, tickets list refreshes to show results for 'critical'""
            }
          ],
          ""finalExpected"": ""Tickets are filtered correctly after each typed query with a 300ms debounce""
        },
        {
          ""title"": ""Verify multiple filter combinations on Support Dashboard"",
          ""precondition"": [
            ""User is on the Support Dashboard (reference UC-1)"",
            ""Filters panel is toggled open""
          ],
          ""steps"": [
            {
              ""action"": ""Select 'New' and 'Pending on L1' in the Status filter"",
              ""expected"": ""Tickets table displays only tickets with status 'New' or 'Pending on L1'""
            },
            {
              ""action"": ""Choose 'High' in the severity filter"",
              ""expected"": ""Results further narrow down to tickets with 'High' severity among the selected statuses""
            },
            {
              ""action"": ""Pick 'Business' from the Support Tier dropdown"",
              ""expected"": ""Ticket list updates once more to reflect the chosen tier""
            }
          ],
          ""finalExpected"": ""Tickets list reflects all selected filter criteria simultaneously""
        },
        {
          ""title"": ""Negative: Verify blank search input returns all tickets"",
          ""precondition"": [
            ""User is on the Support Dashboard (reference UC-1)""
          ],
          ""steps"": [
            {
              ""action"": ""Clear the search box input"",
              ""expected"": ""The search triggers after 300ms, showing the full list of tickets""
            }
          ],
          ""finalExpected"": ""No filter is applied and all available tickets are displayed""
        },
        {
          ""title"": ""Verify infinite scroll on Tickets table"",
          ""precondition"": [
            ""User is on the Support Dashboard (reference UC-1)"",
            ""There are more tickets than fit in the initial view""
          ],
          ""steps"": [
            {
              ""action"": ""Scroll to the bottom of the tickets table"",
              ""expected"": ""Additional tickets load seamlessly without page reload""
            }
          ],
          ""finalExpected"": ""User can continuously scroll and load more tickets until all are shown""
        },
        {
          ""title"": ""Verify sorting by severity in Tickets table"",
          ""precondition"": [
            ""User is on the Support Dashboard (reference UC-1 and 'tickets_master_table')""
          ],
          ""steps"": [
            {
              ""action"": ""Click on the Severity column header"",
              ""expected"": ""Tickets reorder according to the severity from lowest to highest or as specified in the PRD""
            }
          ],
          ""finalExpected"": ""Ticket list is correctly sorted by severity""
        },
        {
          ""title"": ""Verify toggling daily reported tickets from the dashboard header"",
          ""precondition"": [
            ""User is on the Support Dashboard (reference UC-1)""
          ],
          ""steps"": [
            {
              ""action"": ""Enable the 'Daily Reported' toggle in the header"",
              ""expected"": ""Tickets table refreshes to show only daily reported tickets""
            },
            {
              ""action"": ""Disable the 'Daily Reported' toggle"",
              ""expected"": ""Tickets table reverts to showing all tickets""
            }
          ],
          ""finalExpected"": ""Toggling daily reported filter hides or shows relevant tickets""
        },
        {
          ""title"": ""Verify detail pane opening upon ticket selection (Master-Detail)"",
          ""precondition"": [
            ""User is on the Support Dashboard (reference UC-2)"",
            ""Tickets are loaded""
          ],
          ""steps"": [
            {
              ""action"": ""Click a ticket row in the tickets table"",
              ""expected"": ""Detail pane appears on the right with the selected ticket's information""
            }
          ],
          ""finalExpected"": ""Detail pane is displayed with correct data matching the selected ticket""
        },
        {
          ""title"": ""Verify updating ticket fields from the detail pane"",
          ""precondition"": [
            ""User is on the Support Dashboard (reference UC-3)"",
            ""Detail pane is open for a selected ticket""
          ],
          ""steps"": [
            {
              ""action"": ""Change the L2 Owner dropdown to another user"",
              ""expected"": ""Ticket updates with new L2 owner, reflecting immediately in the detail pane""
            },
            {
              ""action"": ""Toggle 'Is Blocked'"",
              ""expected"": ""Ticket's 'isBlocked' field changes and is saved""
            },
            {
              ""action"": ""Disable/enable 'Daily Reported' toggle"",
              ""expected"": ""Ticket's 'isDailyReported' field changes and is saved""
            }
          ],
          ""finalExpected"": ""All ticket field changes persist and display in real time""
        },
        {
          ""title"": ""Verify real-time updates reflect ticket status changes"",
          ""precondition"": [
            ""User is on the Support Dashboard (reference F4)""
          ],
          ""steps"": [
            {
              ""action"": ""Open a second user or session to change a ticket's status externally, or perform a status change internally that triggers real-time refresh"",
              ""expected"": ""The selected ticket's status updates on screen without manual refresh""
            }
          ],
          ""finalExpected"": ""User sees updated ticket status in real-time as described in PRD""
        },
        {
          ""title"": ""Verify stats page refresh on date range change"",
          ""precondition"": [
            ""User is on the Support Stats page (reference UC-4)""
          ],
          ""steps"": [
            {
              ""action"": ""Select a new date range in the date range picker"",
              ""expected"": ""Stats panels (All, Low, Medium, High, etc.) refresh to show updated counts""
            }
          ],
          ""finalExpected"": ""Stats data updates to reflect the selected date range""
        },
        {
          ""title"": ""Verify stats page category tab selection"",
          ""precondition"": [
            ""User is on the Support Stats page (reference UC-4)""
          ],
          ""steps"": [
            {
              ""action"": ""Click on 'Critical' tab in the stats category bar"",
              ""expected"": ""Stats page shows metrics for critical severity tickets""
            },
            {
              ""action"": ""Select 'Low' tab"",
              ""expected"": ""Stats page shows metrics for low severity tickets""
            }
          ],
          ""finalExpected"": ""Selecting different tabs displays corresponding stats""
        },
        {
          ""title"": ""Negative: Verify stats page handling when no data is returned for selected date range"",
          ""precondition"": [
            ""User is on the Support Stats page (reference UC-4)""
          ],
          ""steps"": [
            {
              ""action"": ""Choose a date range with no tickets (e.g., a future date range)"",
              ""expected"": ""Stats panels display zero counts or a clear 'No data' indicator""
            }
          ],
          ""finalExpected"": ""User sees zero or no-data indicators in the stats panels for that date range""
        }
      ]
    }
  }
]","[
    {
        ""rulebook"": {
            ""propertyInheritance"": ""app -> page -> section -> block"",
            ""overridableKeys"": [
                ""visual"",
                ""behaviour""
            ]
        },
        ""app"": {
            ""name"": ""Oracle"",
            ""summary"": ""A support‑ticket dashboard that lets support, engineering and product teams triage, track and update customer issues in real time.\n"",
            ""visual"": ""Light theme with primary color #4B47FF, using Inter font family. Compact information density throughout the application with consistent spacing and typography.\n"",
            ""behaviour"": ""Real-time updates for ticket status changes. Client-side routing for navigation. Infinite scroll for ticket lists. Debounced search and filter operations.\n"",
            ""access"": ""Support\n"",
            ""queries"": [
                {
                    ""id"": ""getTickets"",
                    ""responsibility"": ""Returns a paginated list of tickets with the fields required by the table. Accepts the current filter map and search term. Combines Zendesk API with internal DB to enrich records.\n""
                },
                {
                    ""id"": ""getTicketDetails"",
                    ""responsibility"": ""Fetches full ticket transcript, progress notes, linked Slack thread URL and Drive folder ID for a single ticket id.\n""
                },
                {
                    ""id"": ""getOwners"",
                    ""responsibility"": ""Returns list of available support agents for owner filter dropdown and L2 assignment dropdown in detail pane.\n""
                },
                {
                    ""id"": ""updateTicket"",
                    ""responsibility"": ""Updates ticket fields such as owner, status, severity, daily reporting, or progress notes for a given ticket id. Accepts a ticket id and a map of fields to update. Persists changes to the internal DB and, where relevant, syncs with Zendesk.\n""
                },
                {
                    ""id"": ""getSupportStats"",
                    ""responsibility"": ""Returns ticket counts and breakdowns by severity, status, and support tier for the selected date range and tab. Used to populate the stats panels.\n""
                }
            ],
            ""pages"": [
                {
                    ""id"": ""support_dashboard"",
                    ""title"": ""Support Dashboard"",
                    ""purpose"": ""Provide a single view of all open tickets and their metadata, allow fast filtering, and surface a rich contextual panel for any ticket that the user selects. Everything on this page must feel quick and lightweight so that agents can fly through tickets without waiting for full page loads.\n"",
                    ""visual"": ""Full-width vertical layout with navigation bar at top, header bar below, filters panel below that, and master-detail view at bottom. Navigation bar has primary color background. Header bar is light grey with search and filters. Master-detail view shows ticket table (70%) and detail pane (30%) side by side.\n"",
                    ""behaviour"": ""Real-time ticket updates (runs getTickets and getTicketDetails on ticket changes). Smooth animations for filter panel and detail pane. Client-side routing for navigation. Debounced search at 300ms (runs getTickets query).\n"",
                    ""queries"": [
                        ""getTickets"",
                        ""getTicketDetails"",
                        ""getOwners"",
                        ""updateTicket""
                    ],
                    ""sections"": [
                        {
                            ""id"": ""top_nav"",
                            ""title"": ""Navigation"",
                            ""visual"": ""Full-width navigation bar with primary color background, 56px height. Left-aligned logo and title, right-aligned menu items as white text buttons.\n"",
                            ""behaviour"": ""Always visible. Menu items trigger client-side routing without page reloads.\n"",
                            ""userStory"": ""As a support agent I can navigate quickly"",
                            ""acceptance"": ""Given the app When using navigation Then pages change without reloads"",
                            ""blocks"": [
                                {
                                    ""id"": ""nav_bar"",
                                    ""visual"": ""Horizontal flex container with logo, title, and menu items. Menu items at 85% opacity, 100% on hover.\n"",
                                    ""behaviour"": ""Handles navigation between pages without full reloads.\n"",
                                    ""userStory"": ""As a user I can click menu items"",
                                    ""acceptance"": ""Given app loaded When menu clicked Then navigate""
                                }
                            ]
                        },
                        {
                            ""id"": ""header_bar"",
                            ""title"": ""Header"",
                            ""visual"": ""Full-width light grey (#F7F8FA) bar with subtle bottom shadow. Three zones: title (left), search (center), toggles (right).\n"",
                            ""behaviour"": ""Always visible. Controls visibility of filters panel and daily reported tickets. Search box triggers getTickets query with debounced input.\n"",
                            ""userStory"": ""As an agent I search and toggle options"",
                            ""acceptance"": ""Given dashboard When interacting with header Then UI responds"",
                            ""blocks"": [
                                {
                                    ""id"": ""title_text"",
                                    ""visual"": ""H2 size semi-bold heading displaying \""Oracle\"".\n"",
                                    ""behaviour"": ""Static display element.\n"",
                                    ""userStory"": ""As a user I see the page title"",
                                    ""acceptance"": ""Given dashboard When loaded Then header shows title""
                                },
                                {
                                    ""id"": ""search_box"",
                                    ""visual"": ""Filled-style text input with search icon prefix.\n"",
                                    ""behaviour"": ""Debounced search at 300ms, triggers getTickets query to refresh ticket list.\n"",
                                    ""userStory"": ""As a user I search tickets"",
                                    ""acceptance"": ""Given query When typing Then tickets filter""
                                },
                                {
                                    ""id"": ""daily_reported_toggle"",
                                    ""visual"": ""Compact toggle switch with label.\n"",
                                    ""behaviour"": ""Controls visibility of daily reported tickets in main table (filters getTickets query results).\n"",
                                    ""userStory"": ""As a user I toggle daily reported tickets"",
                                    ""acceptance"": ""Given tickets When toggled Then table updates""
                                },
                                {
                                    ""id"": ""advance_filters_toggle"",
                                    ""visual"": ""Compact toggle switch with label.\n"",
                                    ""behaviour"": ""Controls visibility of filters panel.\n"",
                                    ""userStory"": ""As a user I show filters"",
                                    ""acceptance"": ""Given dashboard When toggle filters Then panel toggles""
                                }
                            ]
                        },
                        {
                            ""id"": ""filters_panel"",
                            ""title"": ""Advanced Filters"",
                            ""visual"": ""White card with 16px padding and 8px rounded corners. Filters arranged in responsive grid layout.\n"",
                            ""behaviour"": ""Expands/collapses with 250ms animation when toggle is enabled. Filter changes trigger getTickets query to refresh ticket list.\n"",
                            ""userStory"": ""As a user I refine ticket lists"",
                            ""acceptance"": ""Given filters When updated Then list refreshes"",
                            ""blocks"": [
                                {
                                    ""id"": ""status_filter"",
                                    ""visual"": ""Multi-select tag control with blue pills and close icons.\n"",
                                    ""behaviour"": ""Pre-populated with New, Pending on L1, Pending on L2 statuses. On change, runs getTickets query.\n"",
                                    ""userStory"": ""As a user I filter by status"",
                                    ""acceptance"": ""Given statuses When selected Then table filters""
                                },
                                {
                                    ""id"": ""owner_filter"",
                                    ""visual"": ""Single-select dropdown with consistent styling.\n"",
                                    ""behaviour"": ""Populated from getOwners query, triggers getTickets query on change.\n"",
                                    ""userStory"": ""As a user I filter by owner"",
                                    ""acceptance"": ""Given owner When selected Then table filters""
                                },
                                {
                                    ""id"": ""severity_filter"",
                                    ""visual"": ""Three checkboxes with colored badges (High=red, Medium=amber, Low=green).\n"",
                                    ""behaviour"": ""Multi-select control, triggers getTickets query on change.\n"",
                                    ""userStory"": ""As a user I filter by severity"",
                                    ""acceptance"": ""Given severities When toggled Then table filters""
                                },
                                {
                                    ""id"": ""product_area_filter"",
                                    ""visual"": ""Dropdown with alphabetically grouped areas.\n"",
                                    ""behaviour"": ""Lazy-loaded, triggers getTickets query on selection.\n"",
                                    ""userStory"": ""As a user I filter by product area"",
                                    ""acceptance"": ""Given area When selected Then table filters""
                                },
                                {
                                    ""id"": ""support_tier_filter"",
                                    ""visual"": ""Standard dropdown with consistent styling.\n"",
                                    ""behaviour"": ""Single-select for Community, Business, Enterprise tiers. Triggers getTickets query on change.\n"",
                                    ""userStory"": ""As a user I filter by support tier"",
                                    ""acceptance"": ""Given tier When selected Then table filters""
                                }
                            ]
                        },
                        {
                            ""id"": ""master_detail_section"",
                            ""title"": ""Ticket List and Details"",
                            ""visual"": ""Full-width two-column layout (70% table, 30% detail) when detail pane is open. Single column (100% table) when detail pane is closed.\n"",
                            ""behaviour"": ""Manages layout transitions between single and two-column views. Handles ticket selection and detail pane visibility (runs getTicketDetails query on selection).\n"",
                            ""userStory"": ""As a user I view a ticket alongside details"",
                            ""acceptance"": ""Given ticket When selected Then detail pane shows"",
                            ""blocks"": [
                                {
                                    ""id"": ""tickets_master_table"",
                                    ""visual"": ""Striped rows with 14px font and compact density. Sticky headers with status badges.\n"",
                                    ""behaviour"": ""Bound to getTickets query. Row click opens detail pane and runs getTicketDetails query. Supports infinite scroll and sorting by Severity and Reported date.\n"",
                                    ""userStory"": ""As a user I scan tickets"",
                                    ""acceptance"": ""Given list When scrolled Then more tickets load""
                                },
                                {
                                    ""id"": ""ticket_detail_pane"",
                                    ""visual"": ""Card container with light border and 24px padding. Section headings in small caps (#6D758D).\n"",
                                    ""behaviour"": ""Visible when ticket is selected. Sticky positioning with vertical scroll. Loads data from getTicketDetails query. Allows updating ticket via updateTicket query. L2 owner dropdown is populated from getOwners query.\n"",
                                    ""userStory"": ""As a user I edit ticket details"",
                                    ""acceptance"": ""Given ticket When updated Then changes save"",
                                    ""blocks"": [
                                        {
                                            ""id"": ""ticket_meta"",
                                            ""visual"": ""Header with report date and external link buttons.\n"",
                                            ""behaviour"": ""Displays report date and opens external links.\n"",
                                            ""userStory"": ""As a user I open external resources"",
                                            ""acceptance"": ""Given ticket When links clicked Then external pages open""
                                        },
                                        {
                                            ""id"": ""l2_owner_dropdown"",
                                            ""visual"": ""Standard dropdown labeled 'L2 Owner'.\n"",
                                            ""behaviour"": ""Assigns ticket to selected L2 support agent.\n"",
                                            ""userStory"": ""As a user I assign L2 owners"",
                                            ""acceptance"": ""Given dropdown When changed Then owner updates""
                                        },
                                        {
                                            ""id"": ""daily_reported_toggle"",
                                            ""visual"": ""Toggle switch with blue/purple active state.\n"",
                                            ""behaviour"": ""Updates ticket daily reporting status.\n"",
                                            ""userStory"": ""As a user I mark daily reporting"",
                                            ""acceptance"": ""Given ticket When toggled Then status saves""
                                        },
                                        {
                                            ""id"": ""blocking_toggles"",
                                            ""visual"": ""Two toggle switches (grey when disabled).\n"",
                                            ""behaviour"": ""Controls 'Is Blocked' and 'Pending Call' statuses.\n"",
                                            ""userStory"": ""As a user I update block statuses"",
                                            ""acceptance"": ""Given ticket When toggles changed Then statuses update""
                                        },
                                        {
                                            ""id"": ""ai_summary_section"",
                                            ""visual"": ""Section with 'AI Summary' heading and text content.\n"",
                                            ""behaviour"": ""Displays AI-generated summary from getTicketDetails.\n"",
                                            ""userStory"": ""As a user I read AI summary"",
                                            ""acceptance"": ""Given ticket When opened Then AI summary shows""
                                        },
                                        {
                                            ""id"": ""progress_section"",
                                            ""visual"": ""Section with numbered list under 'Progress' heading.\n"",
                                            ""behaviour"": ""Shows steps from getTicketDetails.\n"",
                                            ""userStory"": ""As a user I track progress"",
                                            ""acceptance"": ""Given ticket When viewing Then progress list appears""
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                },
                {
                    ""id"": ""support_stats"",
                    ""title"": ""Support Stats"",
                    ""purpose"": ""Provide a comprehensive overview of support ticket metrics, trends, and breakdowns by severity, status, and support tier. Enable teams to monitor performance, identify bottlenecks, and track improvements over time.\n"",
                    ""visual"": ""Full-width layout with date range controls and tabbed interface. Color-coded stat cards for different severity levels.\n"",
                    ""behaviour"": ""Updates stats based on date range and tab selection (runs getSupportStats query). Supports collapsible stat panels.\n"",
                    ""queries"": [
                        ""getSupportStats""
                    ],
                    ""sections"": [
                        {
                            ""id"": ""top_nav"",
                            ""title"": ""Navigation"",
                            ""visual"": ""Full-width navigation bar with primary color background, 56px height. Left-aligned logo and title, right-aligned menu items.\n"",
                            ""behaviour"": ""Always visible. Handles page navigation.\n"",
                            ""userStory"": ""As a user I move between pages"",
                            ""acceptance"": ""Given stats page When nav clicked Then route changes"",
                            ""blocks"": [
                                {
                                    ""id"": ""nav_bar"",
                                    ""visual"": ""Horizontal flex container with logo, title, and menu items.\n"",
                                    ""behaviour"": ""Client-side routing for navigation.\n"",
                                    ""userStory"": ""As a user I access other pages"",
                                    ""acceptance"": ""Given nav When clicked Then stats page changes""
                                }
                            ]
                        },
                        {
                            ""id"": ""stats_header"",
                            ""title"": ""Stats Header"",
                            ""visual"": ""Full-width light grey bar with date pickers and title. Tab bar for ticket categories.\n"",
                            ""behaviour"": ""Controls date range and category selection. Runs getSupportStats query on change.\n"",
                            ""userStory"": ""As a user I pick dates and categories"",
                            ""acceptance"": ""Given stats page When controls change Then data refreshes"",
                            ""blocks"": [
                                {
                                    ""id"": ""stats_title"",
                                    ""visual"": ""H2 size semi-bold heading \""Support Stats\"".\n"",
                                    ""behaviour"": ""Static display element.\n"",
                                    ""userStory"": ""As a user I know the stats page context"",
                                    ""acceptance"": ""Given stats page Then title is visible""
                                },
                                {
                                    ""id"": ""date_range_picker"",
                                    ""visual"": ""Two input fields with calendar icons.\n"",
                                    ""behaviour"": ""Updates getSupportStats query on date selection.\n"",
                                    ""userStory"": ""As a user I filter stats by date"",
                                    ""acceptance"": ""Given range When picked Then stats refresh""
                                },
                                {
                                    ""id"": ""stats_panels"",
                                    ""visual"": ""Tabbed interface with color-coded stat cards. Each card shows count, resolved, and pending metrics.\n"",
                                    ""behaviour"": ""Filters stats by support tier and severity. Runs getSupportStats query on tab/category change.\n"",
                                    ""userStory"": ""As a user I view metrics by severity"",
                                    ""acceptance"": ""Given category When selected Then panels show data"",
                                    ""blocks"": [
                                        {
                                            ""id"": ""stats_tab_bar"",
                                            ""visual"": ""Horizontal tab bar with four categories.\n"",
                                            ""behaviour"": ""Filters stats panels on tab change.\n"",
                                            ""userStory"": ""As a user I switch stat categories"",
                                            ""acceptance"": ""Given tab When clicked Then panels change""
                                        },
                                        {
                                            ""id"": ""all_tickets_panel"",
                                            ""visual"": ""Light blue card with large stat numbers.\n"",
                                            ""behaviour"": ""Always expanded, shows overall metrics.\n"",
                                            ""userStory"": ""As a user I view all ticket stats"",
                                            ""acceptance"": ""Given stats tab When active Then show totals""
                                        },
                                        {
                                            ""id"": ""critical_panel"",
                                            ""visual"": ""Light red card with large stat numbers.\n"",
                                            ""behaviour"": ""Collapsible, shows critical ticket metrics.\n"",
                                            ""userStory"": ""As a user I review critical metrics"",
                                            ""acceptance"": ""Given critical tab When expanded Then metrics show""
                                        },
                                        {
                                            ""id"": ""high_panel"",
                                            ""visual"": ""Light yellow card with large stat numbers.\n"",
                                            ""behaviour"": ""Collapsible, shows high severity metrics.\n"",
                                            ""userStory"": ""As a user I review high metrics"",
                                            ""acceptance"": ""Given high tab When expanded Then metrics show""
                                        },
                                        {
                                            ""id"": ""medium_panel"",
                                            ""visual"": ""Light purple card with large stat numbers.\n"",
                                            ""behaviour"": ""Collapsible, shows medium severity metrics.\n"",
                                            ""userStory"": ""As a user I review medium metrics"",
                                            ""acceptance"": ""Given medium tab When expanded Then metrics show""
                                        },
                                        {
                                            ""id"": ""normal_panel"",
                                            ""visual"": ""Light green card with large stat numbers.\n"",
                                            ""behaviour"": ""Collapsible, shows normal severity metrics.\n"",
                                            ""userStory"": ""As a user I review normal metrics"",
                                            ""acceptance"": ""Given normal tab When expanded Then metrics show""
                                        },
                                        {
                                            ""id"": ""low_panel"",
                                            ""visual"": ""Light grey card with large stat numbers.\n"",
                                            ""behaviour"": ""Collapsible, shows low severity metrics.\n"",
                                            ""userStory"": ""As a user I review low metrics"",
                                            ""acceptance"": ""Given low tab When expanded Then metrics show""
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ]
        }
    }
]","[
    {
        ""metadata"": {
            ""name"": ""Oracle"",
            ""description"": ""A support ticket dashboard that lets support, engineering and product teams triage, track and update customer issues in real time."",
            ""version"": ""1.0.0"",
            ""generatedAt"": ""2025-01-27T10:30:00Z""
        },
        ""product"": {
            ""summary"": ""A support‑ticket dashboard that lets support, engineering and product teams triage, track and update customer issues in real time."",
            ""goals"": [
                ""Enable fast filtering and scanning of support tickets"",
                ""Provide rich contextual details for each ticket"",
                ""Support real-time updates and collaboration"",
                ""Track performance metrics and bottlenecks"",
                ""Integrate with external systems (Zendesk, Slack, Drive)""
            ],
            ""personas"": [
                {
                    ""name"": ""Support Agent"",
                    ""description"": ""Primary user who triages and resolves customer tickets"",
                    ""goals"": [
                        ""Quickly identify high priority tickets"",
                        ""Access complete ticket context"",
                        ""Update ticket status efficiently""
                    ]
                },
                {
                    ""name"": ""Engineering Team"",
                    ""description"": ""Technical staff who resolve complex technical issues"",
                    ""goals"": [
                        ""View technical details"",
                        ""Track blocking issues"",
                        ""Coordinate with support""
                    ]
                },
                {
                    ""name"": ""Product Team"",
                    ""description"": ""Product managers who analyze support trends"",
                    ""goals"": [
                        ""Monitor product areas with issues"",
                        ""Track resolution metrics"",
                        ""Identify improvement opportunities""
                    ]
                }
            ],
            ""features"": [
                {
                    ""id"": ""F1"",
                    ""title"": ""Ticket Dashboard"",
                    ""description"": ""Real-time view of all support tickets with filtering and search"",
                    ""priority"": ""high"",
                    ""domains"": [
                        ""ticket"",
                        ""user""
                    ]
                },
                {
                    ""id"": ""F2"",
                    ""title"": ""Ticket Details"",
                    ""description"": ""Rich contextual pane showing full ticket information and external links"",
                    ""priority"": ""high"",
                    ""domains"": [
                        ""ticket""
                    ]
                },
                {
                    ""id"": ""F3"",
                    ""title"": ""Support Statistics"",
                    ""description"": ""Analytics dashboard showing ticket metrics and trends"",
                    ""priority"": ""medium"",
                    ""domains"": [
                        ""analytics""
                    ]
                },
                {
                    ""id"": ""F4"",
                    ""title"": ""Real-time Updates"",
                    ""description"": ""Live updates when ticket status changes"",
                    ""priority"": ""high"",
                    ""domains"": [
                        ""ticket""
                    ]
                }
            ]
        },
        ""useCases"": [
            {
                ""id"": ""UC-1"",
                ""title"": ""View and filter tickets"",
                ""actors"": [
                    ""Support Agent""
                ],
                ""description"": ""Agent can view all tickets and apply filters to find relevant ones"",
                ""preconditions"": [
                    ""User is logged in"",
                    ""Has access to support dashboard""
                ],
                ""postconditions"": [
                    ""Filtered ticket list is displayed""
                ],
                ""happyPath"": [
                    ""Navigate to support dashboard"",
                    ""Use search box to find specific tickets"",
                    ""Apply filters (status, owner, severity, etc.)"",
                    ""Review filtered results""
                ],
                ""domains"": [
                    ""ticket"",
                    ""user""
                ],
                ""apis"": [
                    ""getTickets"",
                    ""getOwners""
                ],
                ""pages"": [
                    ""/dashboard""
                ]
            },
            {
                ""id"": ""UC-2"",
                ""title"": ""View ticket details"",
                ""actors"": [
                    ""Support Agent"",
                    ""Engineering Team""
                ],
                ""description"": ""User can select a ticket to view full details and external links"",
                ""preconditions"": [
                    ""Tickets are loaded"",
                    ""User has selected a ticket""
                ],
                ""postconditions"": [
                    ""Ticket details are displayed in side pane""
                ],
                ""happyPath"": [
                    ""Click on a ticket in the table"",
                    ""Detail pane opens showing ticket information"",
                    ""View AI summary and progress notes"",
                    ""Access external links (Slack, Drive)""
                ],
                ""domains"": [
                    ""ticket""
                ],
                ""apis"": [
                    ""getTicketDetails""
                ],
                ""pages"": [
                    ""/dashboard""
                ]
            },
            {
                ""id"": ""UC-3"",
                ""title"": ""Update ticket information"",
                ""actors"": [
                    ""Support Agent"",
                    ""Engineering Team""
                ],
                ""description"": ""User can modify ticket fields and save changes"",
                ""preconditions"": [
                    ""Ticket details are displayed"",
                    ""User has edit permissions""
                ],
                ""postconditions"": [
                    ""Ticket is updated in database and external systems""
                ],
                ""happyPath"": [
                    ""Open ticket detail pane"",
                    ""Modify owner, status, or other fields"",
                    ""Toggle daily reporting or blocking status"",
                    ""Changes are automatically saved""
                ],
                ""domains"": [
                    ""ticket""
                ],
                ""apis"": [
                    ""updateTicket""
                ],
                ""pages"": [
                    ""/dashboard""
                ]
            },
            {
                ""id"": ""UC-4"",
                ""title"": ""View support metrics"",
                ""actors"": [
                    ""Product Team"",
                    ""Support Agent""
                ],
                ""description"": ""User can view ticket statistics and trends over time"",
                ""preconditions"": [
                    ""User is logged in"",
                    ""Has access to stats""
                ],
                ""postconditions"": [
                    ""Support metrics are displayed""
                ],
                ""happyPath"": [
                    ""Navigate to support stats page"",
                    ""Select date range"",
                    ""Choose category tab (All, Critical, High, etc.)"",
                    ""Review metrics and trends""
                ],
                ""domains"": [
                    ""analytics""
                ],
                ""apis"": [
                    ""getSupportStats""
                ],
                ""pages"": [
                    ""/stats""
                ]
            }
        ],
        ""userJourneys"": [
            {
                ""id"": ""UJ-1"",
                ""title"": ""Daily ticket triage"",
                ""persona"": ""Support Agent"",
                ""steps"": [
                    {
                        ""page"": ""/dashboard"",
                        ""action"": ""Load dashboard and view new tickets""
                    },
                    {
                        ""page"": ""/dashboard"",
                        ""action"": ""Filter by status: New, Pending on L1""
                    },
                    {
                        ""page"": ""/dashboard"",
                        ""action"": ""Sort by severity to prioritize""
                    },
                    {
                        ""page"": ""/dashboard"",
                        ""action"": ""Click ticket to view details""
                    },
                    {
                        ""page"": ""/dashboard"",
                        ""action"": ""Assign L2 owner if needed""
                    },
                    {
                        ""page"": ""/dashboard"",
                        ""action"": ""Update ticket status""
                    }
                ],
                ""successCriteria"": ""Agent successfully triages new tickets and assigns appropriate owners"",
                ""relatedUseCases"": [
                    ""UC-1"",
                    ""UC-2"",
                    ""UC-3""
                ]
            },
            {
                ""id"": ""UJ-2"",
                ""title"": ""Weekly performance review"",
                ""persona"": ""Product Team"",
                ""steps"": [
                    {
                        ""page"": ""/stats"",
                        ""action"": ""Navigate to support stats""
                    },
                    {
                        ""page"": ""/stats"",
                        ""action"": ""Set date range to last 7 days""
                    },
                    {
                        ""page"": ""/stats"",
                        ""action"": ""Review all ticket metrics""
                    },
                    {
                        ""page"": ""/stats"",
                        ""action"": ""Switch to Critical tab""
                    },
                    {
                        ""page"": ""/stats"",
                        ""action"": ""Analyze critical ticket trends""
                    },
                    {
                        ""page"": ""/stats"",
                        ""action"": ""Check resolution times""
                    }
                ],
                ""successCriteria"": ""Product team understands weekly support performance and identifies trends"",
                ""relatedUseCases"": [
                    ""UC-4""
                ]
            }
        ],
        ""domains"": [
            {
                ""name"": ""ticket"",
                ""description"": ""Support ticket management and tracking"",
                ""models"": [
                    {
                        ""name"": ""Ticket"",
                        ""description"": ""Represents a customer support ticket"",
                        ""fields"": [
                            {
                                ""name"": ""id"",
                                ""type"": ""string"",
                                ""pk"": true
                            },
                            {
                                ""name"": ""orgId"",
                                ""type"": ""string""
                            },
                            {
                                ""name"": ""title"",
                                ""type"": ""string""
                            },
                            {
                                ""name"": ""description"",
                                ""type"": ""string"",
                                ""nullable"": true
                            },
                            {
                                ""name"": ""status"",
                                ""type"": ""TicketStatus""
                            },
                            {
                                ""name"": ""severity"",
                                ""type"": ""TicketSeverity""
                            },
                            {
                                ""name"": ""supportTier"",
                                ""type"": ""SupportTier""
                            },
                            {
                                ""name"": ""productArea"",
                                ""type"": ""string"",
                                ""nullable"": true
                            },
                            {
                                ""name"": ""ownerId"",
                                ""type"": ""string"",
                                ""nullable"": true
                            },
                            {
                                ""name"": ""l2OwnerId"",
                                ""type"": ""string"",
                                ""nullable"": true
                            },
                            {
                                ""name"": ""reportedDate"",
                                ""type"": ""DateTime""
                            },
                            {
                                ""name"": ""isDailyReported"",
                                ""type"": ""boolean""
                            },
                            {
                                ""name"": ""isBlocked"",
                                ""type"": ""boolean""
                            },
                            {
                                ""name"": ""isPendingCall"",
                                ""type"": ""boolean""
                            },
                            {
                                ""name"": ""aiSummary"",
                                ""type"": ""string"",
                                ""nullable"": true
                            },
                            {
                                ""name"": ""progressNotes"",
                                ""type"": ""string"",
                                ""nullable"": true
                            },
                            {
                                ""name"": ""slackThreadUrl"",
                                ""type"": ""string"",
                                ""nullable"": true
                            },
                            {
                                ""name"": ""driveFolderId"",
                                ""type"": ""string"",
                                ""nullable"": true
                            },
                            {
                                ""name"": ""zendeskId"",
                                ""type"": ""string"",
                                ""nullable"": true
                            },
                            {
                                ""name"": ""createdAt"",
                                ""type"": ""DateTime""
                            },
                            {
                                ""name"": ""updatedAt"",
                                ""type"": ""DateTime""
                            }
                        ]
                    },
                    {
                        ""name"": ""TicketStatus"",
                        ""description"": ""Enum for ticket status values"",
                        ""fields"": [
                            {
                                ""name"": ""NEW"",
                                ""type"": ""enum""
                            },
                            {
                                ""name"": ""PENDING_L1"",
                                ""type"": ""enum""
                            },
                            {
                                ""name"": ""PENDING_L2"",
                                ""type"": ""enum""
                            },
                            {
                                ""name"": ""IN_PROGRESS"",
                                ""type"": ""enum""
                            },
                            {
                                ""name"": ""RESOLVED"",
                                ""type"": ""enum""
                            },
                            {
                                ""name"": ""CLOSED"",
                                ""type"": ""enum""
                            }
                        ]
                    },
                    {
                        ""name"": ""TicketSeverity"",
                        ""description"": ""Enum for ticket severity levels"",
                        ""fields"": [
                            {
                                ""name"": ""CRITICAL"",
                                ""type"": ""enum""
                            },
                            {
                                ""name"": ""HIGH"",
                                ""type"": ""enum""
                            },
                            {
                                ""name"": ""MEDIUM"",
                                ""type"": ""enum""
                            },
                            {
                                ""name"": ""NORMAL"",
                                ""type"": ""enum""
                            },
                            {
                                ""name"": ""LOW"",
                                ""type"": ""enum""
                            }
                        ]
                    },
                    {
                        ""name"": ""SupportTier"",
                        ""description"": ""Enum for support tier levels"",
                        ""fields"": [
                            {
                                ""name"": ""COMMUNITY"",
                                ""type"": ""enum""
                            },
                            {
                                ""name"": ""BUSINESS"",
                                ""type"": ""enum""
                            },
                            {
                                ""name"": ""ENTERPRISE"",
                                ""type"": ""enum""
                            }
                        ]
                    }
                ],
                ""apis"": [
                    {
                        ""name"": ""getTickets"",
                        ""path"": ""/trpc/ticket.getTickets"",
                        ""method"": ""query"",
                        ""input"": ""GetTicketsInput"",
                        ""output"": ""PaginatedTicketList"",
                        ""description"": ""Returns paginated list of tickets with filtering and search""
                    },
                    {
                        ""name"": ""getTicketDetails"",
                        ""path"": ""/trpc/ticket.getTicketDetails"",
                        ""method"": ""query"",
                        ""input"": ""GetTicketDetailsInput"",
                        ""output"": ""TicketDetails"",
                        ""description"": ""Fetches full ticket details including transcript and external links""
                    },
                    {
                        ""name"": ""updateTicket"",
                        ""path"": ""/trpc/ticket.updateTicket"",
                        ""method"": ""mutation"",
                        ""input"": ""UpdateTicketInput"",
                        ""output"": ""Ticket"",
                        ""description"": ""Updates ticket fields and syncs with external systems""
                    }
                ],
                ""services"": [
                    {
                        ""name"": ""TicketService"",
                        ""description"": ""Business logic for ticket operations"",
                        ""methods"": [
                            ""getTickets"",
                            ""getTicketById"",
                            ""updateTicket"",
                            ""syncWithZendesk""
                        ]
                    }
                ]
            },
            {
                ""name"": ""user"",
                ""description"": ""User and owner management"",
                ""models"": [
                    {
                        ""name"": ""SupportAgent"",
                        ""description"": ""Support team member who can own tickets"",
                        ""fields"": [
                            {
                                ""name"": ""id"",
                                ""type"": ""string"",
                                ""pk"": true
                            },
                            {
                                ""name"": ""orgId"",
                                ""type"": ""string""
                            },
                            {
                                ""name"": ""email"",
                                ""type"": ""string""
                            },
                            {
                                ""name"": ""fullName"",
                                ""type"": ""string""
                            },
                            {
                                ""name"": ""role"",
                                ""type"": ""string""
                            },
                            {
                                ""name"": ""tier"",
                                ""type"": ""SupportTier""
                            },
                            {
                                ""name"": ""isActive"",
                                ""type"": ""boolean""
                            },
                            {
                                ""name"": ""createdAt"",
                                ""type"": ""DateTime""
                            },
                            {
                                ""name"": ""updatedAt"",
                                ""type"": ""DateTime""
                            }
                        ]
                    }
                ],
                ""apis"": [
                    {
                        ""name"": ""getOwners"",
                        ""path"": ""/trpc/user.getOwners"",
                        ""method"": ""query"",
                        ""input"": ""GetOwnersInput"",
                        ""output"": ""SupportAgent[]"",
                        ""description"": ""Returns list of available support agents for assignment""
                    }
                ],
                ""services"": [
                    {
                        ""name"": ""UserService"",
                        ""description"": ""User management and authentication"",
                        ""methods"": [
                            ""getOwners"",
                            ""getSupportAgents"",
                            ""authenticateUser""
                        ]
                    }
                ]
            },
            {
                ""name"": ""analytics"",
                ""description"": ""Support metrics and reporting"",
                ""models"": [
                    {
                        ""name"": ""SupportStats"",
                        ""description"": ""Aggregated support statistics"",
                        ""fields"": [
                            {
                                ""name"": ""totalTickets"",
                                ""type"": ""number""
                            },
                            {
                                ""name"": ""resolvedTickets"",
                                ""type"": ""number""
                            },
                            {
                                ""name"": ""pendingTickets"",
                                ""type"": ""number""
                            },
                            {
                                ""name"": ""criticalCount"",
                                ""type"": ""number""
                            },
                            {
                                ""name"": ""highCount"",
                                ""type"": ""number""
                            },
                            {
                                ""name"": ""mediumCount"",
                                ""type"": ""number""
                            },
                            {
                                ""name"": ""normalCount"",
                                ""type"": ""number""
                            },
                            {
                                ""name"": ""lowCount"",
                                ""type"": ""number""
                            },
                            {
                                ""name"": ""dateRange"",
                                ""type"": ""DateRange""
                            }
                        ]
                    }
                ],
                ""apis"": [
                    {
                        ""name"": ""getSupportStats"",
                        ""path"": ""/trpc/analytics.getSupportStats"",
                        ""method"": ""query"",
                        ""input"": ""GetSupportStatsInput"",
                        ""output"": ""SupportStats"",
                        ""description"": ""Returns ticket statistics for specified date range and filters""
                    }
                ],
                ""services"": [
                    {
                        ""name"": ""AnalyticsService"",
                        ""description"": ""Support metrics calculation and aggregation"",
                        ""methods"": [
                            ""getSupportStats"",
                            ""calculateMetrics"",
                            ""generateReports""
                        ]
                    }
                ]
            }
        ],
        ""client"": {
            ""pages"": [
                {
                    ""route"": ""/dashboard"",
                    ""title"": ""Support Dashboard"",
                    ""description"": ""Main dashboard for viewing and managing support tickets"",
                    ""organisms"": [
                        ""NavigationBar"",
                        ""HeaderBar"",
                        ""FiltersPanel"",
                        ""TicketsTable"",
                        ""TicketDetailPane""
                    ],
                    ""queries"": [
                        ""getTickets"",
                        ""getTicketDetails"",
                        ""getOwners"",
                        ""updateTicket""
                    ]
                },
                {
                    ""route"": ""/stats"",
                    ""title"": ""Support Stats"",
                    ""description"": ""Analytics dashboard for support metrics and trends"",
                    ""organisms"": [
                        ""NavigationBar"",
                        ""StatsHeader"",
                        ""StatsPanels""
                    ],
                    ""queries"": [
                        ""getSupportStats""
                    ]
                }
            ],
            ""components"": {
                ""atoms"": [
                    {
                        ""name"": ""Button"",
                        ""description"": ""Reusable button component"",
                        ""props"": [
                            ""variant"",
                            ""size"",
                            ""disabled"",
                            ""onClick""
                        ]
                    },
                    {
                        ""name"": ""Input"",
                        ""description"": ""Text input field"",
                        ""props"": [
                            ""value"",
                            ""placeholder"",
                            ""onChange"",
                            ""type""
                        ]
                    },
                    {
                        ""name"": ""Toggle"",
                        ""description"": ""Toggle switch component"",
                        ""props"": [
                            ""checked"",
                            ""onChange"",
                            ""label""
                        ]
                    },
                    {
                        ""name"": ""Badge"",
                        ""description"": ""Status badge for severity and status"",
                        ""props"": [
                            ""variant"",
                            ""text"",
                            ""color""
                        ]
                    },
                    {
                        ""name"": ""Dropdown"",
                        ""description"": ""Select dropdown component"",
                        ""props"": [
                            ""options"",
                            ""value"",
                            ""onChange"",
                            ""placeholder""
                        ]
                    }
                ],
                ""molecules"": [
                    {
                        ""name"": ""SearchBox"",
                        ""description"": ""Search input with icon and debounced handling"",
                        ""props"": [
                            ""value"",
                            ""onChange"",
                            ""placeholder""
                        ],
                        ""atoms"": [
                            ""Input""
                        ]
                    },
                    {
                        ""name"": ""FilterControl"",
                        ""description"": ""Individual filter with label and control"",
                        ""props"": [
                            ""label"",
                            ""type"",
                            ""value"",
                            ""onChange""
                        ],
                        ""atoms"": [
                            ""Dropdown"",
                            ""Toggle""
                        ]
                    },
                    {
                        ""name"": ""StatCard"",
                        ""description"": ""Individual statistics card"",
                        ""props"": [
                            ""title"",
                            ""count"",
                            ""resolved"",
                            ""pending"",
                            ""color""
                        ],
                        ""atoms"": [
                            ""Badge""
                        ]
                    },
                    {
                        ""name"": ""TicketRow"",
                        ""description"": ""Single row in tickets table"",
                        ""props"": [
                            ""ticket"",
                            ""onClick"",
                            ""isSelected""
                        ],
                        ""atoms"": [
                            ""Badge""
                        ]
                    }
                ],
                ""organisms"": [
                    {
                        ""name"": ""NavigationBar"",
                        ""description"": ""Top navigation with logo and menu items"",
                        ""props"": [
                            ""currentPage""
                        ],
                        ""molecules"": [],
                        ""atoms"": [
                            ""Button""
                        ]
                    },
                    {
                        ""name"": ""HeaderBar"",
                        ""description"": ""Page header with search and toggles"",
                        ""props"": [
                            ""searchValue"",
                            ""onSearchChange"",
                            ""showFilters"",
                            ""onToggleFilters""
                        ],
                        ""molecules"": [
                            ""SearchBox""
                        ],
                        ""atoms"": [
                            ""Toggle""
                        ]
                    },
                    {
                        ""name"": ""FiltersPanel"",
                        ""description"": ""Advanced filters panel"",
                        ""props"": [
                            ""isVisible"",
                            ""filters"",
                            ""onFilterChange""
                        ],
                        ""molecules"": [
                            ""FilterControl""
                        ],
                        ""atoms"": [
                            ""Dropdown""
                        ]
                    },
                    {
                        ""name"": ""TicketsTable"",
                        ""description"": ""Main tickets data table"",
                        ""props"": [
                            ""tickets"",
                            ""onTicketSelect"",
                            ""selectedTicketId""
                        ],
                        ""molecules"": [
                            ""TicketRow""
                        ],
                        ""atoms"": [
                            ""Badge""
                        ]
                    },
                    {
                        ""name"": ""TicketDetailPane"",
                        ""description"": ""Side pane showing ticket details"",
                        ""props"": [
                            ""ticket"",
                            ""isVisible"",
                            ""onUpdate""
                        ],
                        ""molecules"": [],
                        ""atoms"": [
                            ""Toggle"",
                            ""Dropdown"",
                            ""Button""
                        ]
                    },
                    {
                        ""name"": ""StatsHeader"",
                        ""description"": ""Stats page header with date controls"",
                        ""props"": [
                            ""dateRange"",
                            ""onDateChange"",
                            ""selectedTab"",
                            ""onTabChange""
                        ],
                        ""molecules"": [],
                        ""atoms"": [
                            ""Input"",
                            ""Button""
                        ]
                    },
                    {
                        ""name"": ""StatsPanels"",
                        ""description"": ""Collection of statistics panels"",
                        ""props"": [
                            ""stats"",
                            ""selectedCategory""
                        ],
                        ""molecules"": [
                            ""StatCard""
                        ],
                        ""atoms"": []
                    }
                ]
            },
            ""stores"": [
                {
                    ""name"": ""ticketStore"",
                    ""description"": ""Global state for ticket data and filters"",
                    ""stateShape"": {
                        ""tickets"": ""Ticket[]"",
                        ""selectedTicket"": ""Ticket | null"",
                        ""filters"": ""TicketFilters"",
                        ""searchTerm"": ""string"",
                        ""isLoading"": ""boolean""
                    },
                    ""actions"": [
                        ""setTickets"",
                        ""selectTicket"",
                        ""updateFilters"",
                        ""setSearchTerm"",
                        ""updateTicket""
                    ]
                },
                {
                    ""name"": ""userStore"",
                    ""description"": ""User authentication and support agents"",
                    ""stateShape"": {
                        ""currentUser"": ""User | null"",
                        ""supportAgents"": ""SupportAgent[]""
                    },
                    ""actions"": [
                        ""setCurrentUser"",
                        ""setSupportAgents""
                    ]
                },
                {
                    ""name"": ""uiStore"",
                    ""description"": ""UI state management"",
                    ""stateShape"": {
                        ""showFilters"": ""boolean"",
                        ""showDetailPane"": ""boolean"",
                        ""isDailyReportedVisible"": ""boolean""
                    },
                    ""actions"": [
                        ""toggleFilters"",
                        ""toggleDetailPane"",
                        ""toggleDailyReported""
                    ]
                },
                {
                    ""name"": ""analyticsStore"",
                    ""description"": ""Support statistics and metrics"",
                    ""stateShape"": {
                        ""supportStats"": ""SupportStats | null"",
                        ""dateRange"": ""DateRange"",
                        ""selectedCategory"": ""string""
                    },
                    ""actions"": [
                        ""setSupportStats"",
                        ""setDateRange"",
                        ""setSelectedCategory""
                    ]
                }
            ]
        }
    }
]","[
    {
        ""testSuite"": {
            ""name"": ""CRM test suite"",
            ""description"": ""Validate navigation in the CRM app"",
            ""testCases"": [
                {
                    ""title"": ""Navigate Spec"",
                    ""precondition"": [
                        ""User has logged in""
                    ],
                    ""steps"": [
                        {
                            ""action"": ""Navigate to page1"",
                            ""expected"": """"
                        },
                        {
                            ""action"": ""Navigate to users"",
                            ""expected"": """"
                        }
                    ],
                    ""finalExpected"": ""User has successfully navigated to the page""
                },
                {
                    ""title"": ""Navigate Spec"",
                    ""precondition"": [
                        ""User has logged in""
                    ],
                    ""steps"": [
                        {
                            ""action"": ""Navigate to page1"",
                            ""expected"": """"
                        },
                        {
                            ""action"": ""Navigate to users"",
                            ""expected"": """"
                        }
                    ],
                    ""finalExpected"": ""User has successfully navigated to the page""
                }
            ]
        }
    }
]"
"[
  {
    ""suiteName"": ""Taskmate Functional Test Suite"",
    ""suiteDescription"": ""Comprehensive functional test plan covering all end-user UI functionalities for Taskmate, per PRD/TD specifications."",
    ""testCases"": [
      {
        ""title"": ""Verify My Tasks page loads with empty state if no tasks exist"",
        ""preconditions"": [
          ""User is logged in"",
          ""No existing tasks""
        ],
        ""steps"": [
          {
            ""action"": ""Open Taskmate application"",
            ""expectedResult"": ""My Tasks page is displayed with header, add task section, and an empty tasks list message.""
          },
          {
            ""action"": ""Check presence of My Tasks header"",
            ""expectedResult"": ""Header 'My Tasks' is visible as described in PRD (home > header_section).""
          }
        ],
        ""finalExpectedOutcome"": ""User sees an empty state with no tasks as per PRD (home > tasks_list_section), correlating with UC-manage_daily_tasks and UJ-first_time_user.""
      },
      {
        ""title"": ""Add a new task from My Tasks page"",
        ""preconditions"": [
          ""User is logged in"",
          ""User is on My Tasks page""
        ],
        ""steps"": [
          {
            ""action"": ""Enter a task description in the input field (e.g. 'Buy groceries')"",
            ""expectedResult"": ""Task input field populates with 'Buy groceries'.""
          },
          {
            ""action"": ""Click the Add button"",
            ""expectedResult"": ""A new task card with the text 'Buy groceries' appears in Active Tasks. Input field clears after addition.""
          }
        ],
        ""finalExpectedOutcome"": ""User sees the new task in the active tasks list per PRD (home > tasks_list_section), supporting UC-manage_daily_tasks and UJ-first_time_user.""
      },
      {
        ""title"": ""Prevent adding an empty task"",
        ""preconditions"": [
          ""User is logged in"",
          ""User is on My Tasks page""
        ],
        ""steps"": [
          {
            ""action"": ""Leave the task input field blank"",
            ""expectedResult"": ""Add button is disabled as specified in PRD (home > add_task_section).""
          },
          {
            ""action"": ""Attempt to press Enter or click Add button"",
            ""expectedResult"": ""No task is created. The system prevents submission of empty tasks.""
          }
        ],
        ""finalExpectedOutcome"": ""Empty tasks cannot be added, ensuring valid entries only. Aligns with UC-manage_daily_tasks.""
      },
      {
        ""title"": ""Complete a task from My Tasks page (inline)"",
        ""preconditions"": [
          ""User is logged in"",
          ""At least one active task exists in My Tasks""
        ],
        ""steps"": [
          {
            ""action"": ""Click the checkbox of an active task"",
            ""expectedResult"": ""Task is visually marked completed and removed from active tasks, reflecting PRD (home > tasks_list_section) with a completion animation.""
          }
        ],
        ""finalExpectedOutcome"": ""Task moves to Completed Tasks as described in UC-manage_daily_tasks and UJ-first_time_user.""
      },
      {
        ""title"": ""Edit a task inline from My Tasks page"",
        ""preconditions"": [
          ""User is logged in"",
          ""At least one active task exists in My Tasks""
        ],
        ""steps"": [
          {
            ""action"": ""Click the Edit icon on an existing task"",
            ""expectedResult"": ""A modal or inline editor appears displaying current task text, per PRD (home > tasks_list_section).""
          },
          {
            ""action"": ""Change the task text to 'Buy milk' and confirm"",
            ""expectedResult"": ""Task text updates to 'Buy milk' in real-time; the updated text is now visible in the list.""
          }
        ],
        ""finalExpectedOutcome"": ""Task text is successfully updated, demonstrating UC-manage_daily_tasks.""
      },
      {
        ""title"": ""Delete a task from My Tasks page"",
        ""preconditions"": [
          ""User is logged in"",
          ""At least one active task exists in My Tasks""
        ],
        ""steps"": [
          {
            ""action"": ""Click the Delete icon on a specific task"",
            ""expectedResult"": ""The task is removed from the Active Tasks list with an animation, as per PRD (home > tasks_list_section).""
          }
        ],
        ""finalExpectedOutcome"": ""Task is removed from My Tasks, consistent with UC-manage_daily_tasks.""
      },
      {
        ""title"": ""View and restore a completed task from Completed Tasks page"",
        ""preconditions"": [
          ""User is logged in"",
          ""At least one completed task exists""
        ],
        ""steps"": [
          {
            ""action"": ""From My Tasks page, click 'Completed' link in the nav bar"",
            ""expectedResult"": ""Completed Tasks page loads, showing a list of completed tasks with greyed-out text and checkmark icons.""
          },
          {
            ""action"": ""Click the Restore (Undo) icon on a completed task"",
            ""expectedResult"": ""Task is removed from Completed Tasks and reinstated in My Tasks as active.""
          }
        ],
        ""finalExpectedOutcome"": ""The user restores a completed task as per PRD (completed > completed_tasks_section), adhering to UC-review_completed_tasks.""
      },
      {
        ""title"": ""Permanently delete a completed task from Completed Tasks page"",
        ""preconditions"": [
          ""User is logged in"",
          ""At least one completed task exists""
        ],
        ""steps"": [
          {
            ""action"": ""Navigate to Completed Tasks page"",
            ""expectedResult"": ""Completed tasks are listed with options to restore or delete, as per PRD (completed > completed_tasks_section).""
          },
          {
            ""action"": ""Click the Delete icon on a completed task and confirm if prompted"",
            ""expectedResult"": ""Task is permanently removed from Completed Tasks; it is not visible in My Tasks or Completed afterward.""
          }
        ],
        ""finalExpectedOutcome"": ""User permanently removes a completed task, fulfilling UC-review_completed_tasks.""
      },
      {
        ""title"": ""Create a new task list from Task Lists page"",
        ""preconditions"": [
          ""User is logged in"",
          ""User is on Task Lists page""
        ],
        ""steps"": [
          {
            ""action"": ""Click 'Add List' button"",
            ""expectedResult"": ""A modal opens to enter a new list name, per PRD (task_lists > lists_header).""
          },
          {
            ""action"": ""Enter a valid list name (e.g. 'Work') and confirm"",
            ""expectedResult"": ""A new list card titled 'Work' appears in the grid with 0 tasks.""
          }
        ],
        ""finalExpectedOutcome"": ""A new task list is created, corresponding to UC-organize_task_lists and UJ-power_user_workflow.""
      },
      {
        ""title"": ""Prevent creating a new list with an empty name"",
        ""preconditions"": [
          ""User is logged in"",
          ""User is on Task Lists page""
        ],
        ""steps"": [
          {
            ""action"": ""Click 'Add List' button and leave the name field blank"",
            ""expectedResult"": ""Creation is prevented; the confirm button is disabled or an error is shown, per PRD (task_lists > lists_header).""
          },
          {
            ""action"": ""Attempt to close or correct the list name"",
            ""expectedResult"": ""User can discard changes or only proceed by entering a valid name.""
          }
        ],
        ""finalExpectedOutcome"": ""System enforces valid list names, blocking empty submissions, supporting UC-organize_task_lists.""
      },
      {
        ""title"": ""Edit an existing Task List's name"",
        ""preconditions"": [
          ""User is logged in"",
          ""At least one existing list on Task Lists page""
        ],
        ""steps"": [
          {
            ""action"": ""Click the Edit icon on a specific list card"",
            ""expectedResult"": ""A modal appears with the current list name, as per PRD (task_lists > lists_grid_section).""
          },
          {
            ""action"": ""Change the list name and confirm"",
            ""expectedResult"": ""List name updates in real-time in the Lists grid.""
          }
        ],
        ""finalExpectedOutcome"": ""User updates a list name successfully, per UC-organize_task_lists.""
      },
      {
        ""title"": ""Delete an existing Task List"",
        ""preconditions"": [
          ""User is logged in"",
          ""At least one existing list on Task Lists page""
        ],
        ""steps"": [
          {
            ""action"": ""Click the Delete icon on a specific list card"",
            ""expectedResult"": ""A confirmation dialog appears, warning about task removal. PRD (task_lists > lists_grid_section).""
          },
          {
            ""action"": ""Confirm deletion"",
            ""expectedResult"": ""List is removed from the grid along with associated tasks.""
          }
        ],
        ""finalExpectedOutcome"": ""User permanently removes the list and its tasks, as per UC-organize_task_lists.""
      },
      {
        ""title"": ""Add a new task within a specific list detail page"",
        ""preconditions"": [
          ""User is logged in"",
          ""At least one custom list exists"",
          ""User is viewing that list detail page""
        ],
        ""steps"": [
          {
            ""action"": ""Enter a task description in the list detail input field (e.g. 'Prepare report')"",
            ""expectedResult"": ""Input field is populated with 'Prepare report'.""
          },
          {
            ""action"": ""Click Add"",
            ""expectedResult"": ""A new task is displayed in the list detail's tasks section. The list's active count updates.""
          }
        ],
        ""finalExpectedOutcome"": ""User sees the new task in the list detail, supporting UC-organize_task_lists and UJ-power_user_workflow.""
      },
      {
        ""title"": ""Complete a task inside the list detail page"",
        ""preconditions"": [
          ""User is logged in"",
          ""At least one active task in the selected list detail""
        ],
        ""steps"": [
          {
            ""action"": ""Click the checkbox for the task"",
            ""expectedResult"": ""Task is marked completed and removed from active tasks in that list; completed count updates for this list.""
          }
        ],
        ""finalExpectedOutcome"": ""User marks a list-specific task as completed, referencing UC-manage_daily_tasks.""
      },
      {
        ""title"": ""Navigate between My Tasks, Completed, and Task Lists pages using header nav"",
        ""preconditions"": [
          ""User is logged in"",
          ""User is on My Tasks page""
        ],
        ""steps"": [
          {
            ""action"": ""Click 'Completed' in the nav bar"",
            ""expectedResult"": ""Completed Tasks page loads with previously completed items.""
          },
          {
            ""action"": ""Click 'Task Lists' in the nav bar"",
            ""expectedResult"": ""Task Lists page loads, showing a grid of existing lists.""
          },
          {
            ""action"": ""Click 'My Tasks' in the nav bar"",
            ""expectedResult"": ""User is returned to the My Tasks page with state preserved.""
          }
        ],
        ""finalExpectedOutcome"": ""User can switch seamlessly among pages as per PRD (header_section > nav_bar), covering UC-manage_daily_tasks, UC-review_completed_tasks, and UC-organize_task_lists.""
      }
    ]
  }
]","[
  {
    ""app"": {
      ""name"": ""Taskmate"",
      ""summary"": ""A simple To-Do app that helps users manage daily tasks. Users can add, edit, complete, and delete tasks across multiple lists and view completed history.\n"",
      ""visual"": ""Light theme with primary color #29A19C. Uses a clean and minimal design, Roboto font, and provides a comfortable information density suitable for everyday productivity.\n"",
      ""behaviour"": ""Instant feedback for task actions (add, edit, complete, delete). Client-side routing between pages. Uses modals for editing tasks. Shows real-time task updates and uses local storage for state persistence.\n"",
      ""access"": ""All logged-in users.\n"",
      ""pages"": [
        {
          ""id"": ""home"",
          ""title"": ""My Tasks"",
          ""purpose"": ""Displays the user's active to-dos. Allows adding, editing, completing, or deleting tasks.\n"",
          ""visual"": ""Centered layout with header at the top, task input bar, and a vertical list of current tasks. Each task has checkbox, text, and action buttons.\n"",
          ""behaviour"": ""Loads all uncompleted tasks. Animations for task addition, completion, and deletion. Inline editing available. Shows empty state when no tasks exist.\n"",
          ""sections"": [
            {
              ""id"": ""header_section"",
              ""title"": ""Header"",
              ""visual"": ""Fixed top bar, full width, primary color background, white text, 56px height. Contains app logo and navigation links (My Tasks, Completed, Task Lists).\n"",
              ""behaviour"": ""Navigates between main app pages on link click.\n"",
              ""blocks"": [
                {
                  ""id"": ""nav_bar"",
                  ""visual"": ""Horizontal flex: logo left, links (My Tasks, Completed, Task Lists) right, each as button with white text and hover underline.\n"",
                  ""behaviour"": ""Triggers page navigation on click.\n""
                }
              ]
            },
            {
              ""id"": ""add_task_section"",
              ""title"": ""Add Task"",
              ""visual"": ""Card with white background, 24px padding, drop shadow. Contains a single row: input (70%) and Add button (30%).\n"",
              ""behaviour"": ""Input auto-focuses on load. Enter key or Add button inserts new task.\n"",
              ""blocks"": [
                {
                  ""id"": ""task_input"",
                  ""visual"": ""Rounded text field, placeholder \""What needs to be done?\"", left aligned.\n"",
                  ""behaviour"": ""Accepts task text, prevents empty submissions.\n""
                },
                {
                  ""id"": ""add_task_button"",
                  ""visual"": ""Primary color raised button, white \""Add\"" label, right aligned.\n"",
                  ""behaviour"": ""On click, adds task and clears input. Disabled if input empty.\n""
                }
              ]
            },
            {
              ""id"": ""tasks_list_section"",
              ""title"": ""Active Tasks"",
              ""visual"": ""Vertically stacked list of tasks, each in a white card with shadow. 12px margin between tasks. Checkbox left, text center, icons right.\n"",
              ""behaviour"": ""Animates task addition/removal. Updates on any data change.\n"",
              ""blocks"": [
                {
                  ""id"": ""task_item_block"",
                  ""visual"": ""Horizontal row: left (checkbox), center (task text, editable inline), right (edit and delete icons as buttons).\n"",
                  ""behaviour"": ""Checkbox marks complete. Edit icon opens modal. Delete icon removes task.\n""
                }
              ]
            }
          ]
        },
        {
          ""id"": ""completed"",
          ""title"": ""Completed Tasks"",
          ""purpose"": ""Shows all tasks the user has completed.\n"",
          ""visual"": ""Stacked list of completed tasks: greyed out text, checkmark icons, date completed on right. Header bar with back arrow.\n"",
          ""behaviour"": ""Loads completed tasks on entry. Allows deleting or restoring tasks.\n"",
          ""sections"": [
            {
              ""id"": ""header_section"",
              ""title"": ""Header"",
              ""visual"": ""Fixed bar with white background and thin bottom border. Contains back arrow and \""Completed Tasks\"" title.\n"",
              ""behaviour"": ""Back arrow returns to My Tasks page.\n"",
              ""blocks"": [
                {
                  ""id"": ""completed_header"",
                  ""visual"": ""Left (back arrow icon button), center (\""Completed Tasks\"" title, bold).\n"",
                  ""behaviour"": ""Back arrow navigates to home page.\n""
                }
              ]
            },
            {
              ""id"": ""completed_tasks_section"",
              ""title"": ""List"",
              ""visual"": ""List format, each item in a pale grey card, 8px margin, green checkmark, strikethrough text for task, completion date right.\n"",
              ""behaviour"": ""Animated entry/removal. Buttons restore ('undo') or permanently delete task.\n"",
              ""blocks"": [
                {
                  ""id"": ""completed_task_block"",
                  ""visual"": ""Horizontal: green checkmark icon, strikethrough task text left, greyed-out, small date right, restore and delete icons far right.\n"",
                  ""behaviour"": ""Restore puts task back on active list. Delete removes forever.\n""
                }
              ]
            }
          ]
        },
        {
          ""id"": ""task_lists"",
          ""title"": ""Task Lists"",
          ""purpose"": ""Allow user to organize tasks into named lists (e.g. Work, Home, Shopping).\n"",
          ""visual"": ""Grid of colored cards, one per list, each showing list name and active/completed counts. Add List button at top right.\n"",
          ""behaviour"": ""Clicking a list navigates to list detail. Add List opens modal for new list name. Deleting a list removes all its tasks.\n"",
          ""sections"": [
            {
              ""id"": ""lists_header"",
              ""title"": ""Lists Header"",
              ""visual"": ""Top bar with title \""Task Lists\"" left and Add List button right.\n"",
              ""behaviour"": ""Add List button opens modal.\n"",
              ""blocks"": [
                {
                  ""id"": ""lists_title"",
                  ""visual"": ""H2 bold left, no icon.\n"",
                  ""behaviour"": ""Static display.\n""
                },
                {
                  ""id"": ""add_list_button"",
                  ""visual"": ""Circular primary color FAB with plus icon, white.\n"",
                  ""behaviour"": ""Opens create-list modal.\n""
                }
              ]
            },
            {
              ""id"": ""lists_grid_section"",
              ""title"": ""Lists Grid"",
              ""visual"": ""Responsive card grid, 2–3 columns on large screens, stacked on mobile. Each card: colored top border, list name, active/complete counts, edit/delete buttons.\n"",
              ""behaviour"": ""Card click navigates to list detail. Edit opens modal. Delete confirms before removal.\n"",
              ""blocks"": [
                {
                  ""id"": ""list_card_block"",
                  ""visual"": ""Card: colored border top, bold list name, counts below, faded edit and delete icons bottom right.\n"",
                  ""behaviour"": ""Edit triggers modal. Delete shows confirm dialog.\n""
                }
              ]
            }
          ]
        },
        {
          ""id"": ""list_detail"",
          ""title"": ""List Detail"",
          ""purpose"": ""View and manage tasks for a specific named list.\n"",
          ""visual"": ""Same layout as My Tasks but scoped to the selected list. Header displays list name with colored accent.\n"",
          ""behaviour"": ""Loads the selected list's tasks. Allows full CRUD and completion. Return to Task Lists page via header.\n"",
          ""sections"": [
            {
              ""id"": ""list_detail_header"",
              ""title"": ""Header"",
              ""visual"": ""Colored header bar with list name and back arrow on left.\n"",
              ""behaviour"": ""Back arrow navigates back to Task Lists.\n"",
              ""blocks"": [
                {
                  ""id"": ""list_detail_nav"",
                  ""visual"": ""Row with back arrow, list name (bold, colored), count badge right.\n"",
                  ""behaviour"": ""Handles back navigation.\n""
                }
              ]
            },
            {
              ""id"": ""list_add_task_section"",
              ""title"": ""Add List Task"",
              ""visual"": ""Inline with previous Add Task layout. Background has list's accent color shadow.\n"",
              ""behaviour"": ""Add and input same as main Add Task section.\n"",
              ""blocks"": [
                {
                  ""id"": ""list_task_input"",
                  ""visual"": ""As with home task_input, with hint \""Add a task to this list\"".\n"",
                  ""behaviour"": ""Accepts input, adds task on click or enter.\n""
                },
                {
                  ""id"": ""list_add_task_button"",
                  ""visual"": ""Primary colored button, \""Add\"", right aligned.\n"",
                  ""behaviour"": ""Adds task if input not empty.\n""
                }
              ]
            },
            {
              ""id"": ""list_tasks_list_section"",
              ""title"": ""List's Tasks"",
              ""visual"": ""Like home page tasks, in vertical list. Items show task, complete, edit, and delete features.\n"",
              ""behaviour"": ""List only updates tasks in this specific list. Animates changes.\n"",
              ""blocks"": [
                {
                  ""id"": ""list_task_item_block"",
                  ""visual"": ""Same as main task_item_block but scoped to current list.\n"",
                  ""behaviour"": ""All task actions scoped to list context.\n""
                }
              ]
            }
          ]
        }
      ],
      ""queries"": [
        {
          ""id"": ""fetchTasks"",
          ""responsibility"": ""Retrieves all user tasks and their attributes from storage (local or backend). Used to populate active, completed, and list-specific task tables.\n""
        },
        {
          ""id"": ""addTask"",
          ""responsibility"": ""Inserts a new task into the user's current list or default list and returns the updated task list.\n""
        },
        {
          ""id"": ""editTask"",
          ""responsibility"": ""Updates the text or details of an existing task. Handles in-line and modal editing.\n""
        },
        {
          ""id"": ""completeTask"",
          ""responsibility"": ""Marks a task as completed and updates the lists accordingly.\n""
        },
        {
          ""id"": ""deleteTask"",
          ""responsibility"": ""Removes a specific task from the user's list or completed history.\n""
        },
        {
          ""id"": ""fetchLists"",
          ""responsibility"": ""Retrieves all named user lists, their task counts, and list colors.\n""
        },
        {
          ""id"": ""addList"",
          ""responsibility"": ""Creates a new named list for organizing tasks.\n""
        },
        {
          ""id"": ""editList"",
          ""responsibility"": ""Updates the name or color of a specific list.\n""
        },
        {
          ""id"": ""deleteList"",
          ""responsibility"": ""Deletes a list and all its tasks after confirmation.\n""
        }
      ]
    },
    ""mermaid"": ""graph TD\n  A[App: Taskmate]\n  A --> B[Page: My Tasks]\n  A --> C[Page: Completed Tasks]\n  A --> D[Page: Task Lists]\n  A --> E[Page: List Detail]\n\n  B --> B1[Section: Header]\n  B --> B2[Section: Add Task]\n  B --> B3[Section: Active Tasks]\n  B1 --> B1a[Block: nav_bar]\n  B2 --> B2a[Block: task_input]\n  B2 --> B2b[Block: add_task_button]\n  B3 --> B3a[Block: task_item_block]\n\n  C --> C1[Section: Header]\n  C --> C2[Section: List]\n  C1 --> C1a[Block: completed_header]\n  C2 --> C2a[Block: completed_task_block]\n\n  D --> D1[Section: Lists Header]\n  D --> D2[Section: Lists Grid]\n  D1 --> D1a[Block: lists_title]\n  D1 --> D1b[Block: add_list_button]\n  D2 --> D2a[Block: list_card_block]\n\n  E --> E1[Section: List Detail Header]\n  E --> E2[Section: Add List Task]\n  E --> E3[Section: List's Tasks]\n  E1 --> E1a[Block: list_detail_nav]\n  E2 --> E2a[Block: list_task_input]\n  E2 --> E2b[Block: list_add_task_button]\n  E3 --> E3a[Block: list_task_item_block]\n""
  }
]","[
  {
    ""metadata"": {
      ""name"": ""Taskmate Technical Specification"",
      ""description"": ""Technical documentation for Taskmate To-Do application"",
      ""version"": ""1.0.0"",
      ""generatedAt"": ""2024-12-19T10:30:00Z""
    },
    ""product"": {
      ""summary"": ""A simple To-Do app that helps users manage daily tasks. Users can add, edit, complete, and delete tasks across multiple lists and view completed history."",
      ""goals"": [
        ""Enable users to efficiently manage daily tasks"",
        ""Provide organized task lists for different contexts"",
        ""Maintain task completion history for reference"",
        ""Offer intuitive and responsive user interface""
      ],
      ""personas"": [
        {
          ""name"": ""Daily Task Manager"",
          ""description"": ""Users who need to organize and track their daily activities"",
          ""needs"": [
            ""Quick task entry and completion"",
            ""Organized task lists by context"",
            ""Task completion history""
          ]
        },
        {
          ""name"": ""Productivity Enthusiast"",
          ""description"": ""Users who want detailed task organization across multiple categories"",
          ""needs"": [
            ""Multiple task lists"",
            ""Task editing capabilities"",
            ""Visual task management""
          ]
        }
      ],
      ""features"": [
        {
          ""name"": ""Task Management"",
          ""description"": ""Core CRUD operations for tasks"",
          ""priority"": ""high""
        },
        {
          ""name"": ""Task Lists"",
          ""description"": ""Organize tasks into named categories"",
          ""priority"": ""high""
        },
        {
          ""name"": ""Completion History"",
          ""description"": ""Track and review completed tasks"",
          ""priority"": ""medium""
        },
        {
          ""name"": ""Responsive Design"",
          ""description"": ""Mobile-friendly interface"",
          ""priority"": ""high""
        }
      ]
    },
    ""useCases"": [
      {
        ""id"": ""manage_daily_tasks"",
        ""title"": ""Manage Daily Tasks"",
        ""description"": ""User adds, edits, completes, and deletes tasks"",
        ""actor"": ""Task Manager"",
        ""steps"": [
          ""User navigates to My Tasks page"",
          ""User adds new task via input field"",
          ""User marks tasks as complete using checkbox"",
          ""User edits task text inline or via modal"",
          ""User deletes unwanted tasks""
        ]
      },
      {
        ""id"": ""organize_task_lists"",
        ""title"": ""Organize Task Lists"",
        ""description"": ""User creates and manages multiple task lists"",
        ""actor"": ""Productivity Enthusiast"",
        ""steps"": [
          ""User navigates to Task Lists page"",
          ""User creates new list with custom name"",
          ""User assigns tasks to specific lists"",
          ""User manages list-specific tasks""
        ]
      },
      {
        ""id"": ""review_completed_tasks"",
        ""title"": ""Review Completed Tasks"",
        ""description"": ""User views and manages completed task history"",
        ""actor"": ""Task Manager"",
        ""steps"": [
          ""User navigates to Completed Tasks page"",
          ""User reviews completed tasks with dates"",
          ""User restores tasks to active list if needed"",
          ""User permanently deletes completed tasks""
        ]
      }
    ],
    ""userJourneys"": [
      {
        ""id"": ""first_time_user"",
        ""title"": ""First Time User Experience"",
        ""description"": ""New user creates their first task and explores the app"",
        ""steps"": [
          {
            ""step"": ""User opens Taskmate application"",
            ""page"": ""home"",
            ""action"": ""App loads with empty state""
          },
          {
            ""step"": ""User adds their first task"",
            ""page"": ""home"",
            ""action"": ""Clicks task input, types task, clicks Add button""
          },
          {
            ""step"": ""User completes the task"",
            ""page"": ""home"",
            ""action"": ""Clicks checkbox to mark complete""
          },
          {
            ""step"": ""User views completed tasks"",
            ""page"": ""completed"",
            ""action"": ""Navigates to Completed Tasks page""
          }
        ]
      },
      {
        ""id"": ""power_user_workflow"",
        ""title"": ""Power User Managing Multiple Lists"",
        ""description"": ""Experienced user managing tasks across multiple organized lists"",
        ""steps"": [
          {
            ""step"": ""User creates work task list"",
            ""page"": ""task_lists"",
            ""action"": ""Clicks Add List, creates 'Work' list""
          },
          {
            ""step"": ""User adds work tasks"",
            ""page"": ""list_detail"",
            ""action"": ""Adds multiple work-related tasks""
          },
          {
            ""step"": ""User creates home task list"",
            ""page"": ""task_lists"",
            ""action"": ""Creates 'Home' list""
          }
        ]
      }
    ]
  }
]","[
    {
        ""testSuite"": {
            ""name"": ""CRM test suite"",
            ""description"": ""Validate navigation in the CRM app"",
            ""testCases"": [
                {
                    ""title"": ""Navigate Spec"",
                    ""precondition"": [
                        ""User has logged in""
                    ],
                    ""steps"": [
                        {
                            ""action"": ""Navigate to page1"",
                            ""expected"": """"
                        },
                        {
                            ""action"": ""Navigate to users"",
                            ""expected"": """"
                        }
                    ],
                    ""finalExpected"": ""User has successfully navigated to the page""
                },
                {
                    ""title"": ""Navigate Spec"",
                    ""precondition"": [
                        ""User has logged in""
                    ],
                    ""steps"": [
                        {
                            ""action"": ""Navigate to page1"",
                            ""expected"": """"
                        },
                        {
                            ""action"": ""Navigate to users"",
                            ""expected"": """"
                        }
                    ],
                    ""finalExpected"": ""User has successfully navigated to the page""
                }
            ]
        }
    }
]"
"[
  {
    ""suiteName"": ""Simple CMS Functional Test Suite"",
    ""suiteDescription"": ""UI functional verification covering core pages and workflows for the Simple CMS application"",
    ""testCases"": [
      {
        ""title"": ""Verify navigation from Dashboard to Create Post page"",
        ""preconditions"": [
          ""User is authenticated and on the Dashboard page""
        ],
        ""steps"": [
          {
            ""action"": ""Click on the 'New Post' button at the top right"",
            ""expectedResult"": ""User is navigated to the Create Post page showing an empty post form""
          }
        ],
        ""finalExpectedOutcome"": ""The Create Post form is displayed, ready for new content creation""
      },
      {
        ""title"": ""Verify unsaved changes warning when navigating away from Create Post page"",
        ""preconditions"": [
          ""User is authenticated and on the Create Post page""
        ],
        ""steps"": [
          {
            ""action"": ""Enter some text in the Title field without saving"",
            ""expectedResult"": ""Unsaved changes are detected""
          },
          {
            ""action"": ""Attempt to navigate back to the Dashboard page"",
            ""expectedResult"": ""A prompt warns that there are unsaved changes and asks for confirmation""
          }
        ],
        ""finalExpectedOutcome"": ""User is prevented from losing unsaved changes unless they confirm navigation""
      },
      {
        ""title"": ""Create new blog post with valid data"",
        ""preconditions"": [
          ""User is authenticated and on the Create Post page""
        ],
        ""steps"": [
          {
            ""action"": ""Enter a valid title (up to 150 characters) in the Title field"",
            ""expectedResult"": ""No validation errors appear for the Title field""
          },
          {
            ""action"": ""Enter content in the rich text editor (e.g., paragraphs, bold text)"",
            ""expectedResult"": ""Rich text editor accepts and displays the formatted content""
          },
          {
            ""action"": ""Optionally add up to 5 tags in the Tags field"",
            ""expectedResult"": ""Tags appear as removable badges, and no error is shown""
          },
          {
            ""action"": ""(Optional) Check 'Publish immediately?' if the post should be published"",
            ""expectedResult"": ""Publish toggle is activated""
          },
          {
            ""action"": ""Click 'Save' to submit the form"",
            ""expectedResult"": ""The post is successfully created, and the user is redirected to the Dashboard""
          }
        ],
        ""finalExpectedOutcome"": ""A new post is visible in the Dashboard with the expected title, tags, and status""
      },
      {
        ""title"": ""Create new blog post without mandatory Title (negative scenario)"",
        ""preconditions"": [
          ""User is authenticated and on the Create Post page""
        ],
        ""steps"": [
          {
            ""action"": ""Leave the Title field blank"",
            ""expectedResult"": ""Form validation indicates the Title is required""
          },
          {
            ""action"": ""Enter content in the rich text editor"",
            ""expectedResult"": ""Editor behaves normally and accepts the content""
          },
          {
            ""action"": ""Click 'Save'"",
            ""expectedResult"": ""Form submission is blocked; the user is shown a Title-required validation error""
          }
        ],
        ""finalExpectedOutcome"": ""User cannot create a post without a valid Title; they remain on the Create Post page""
      },
      {
        ""title"": ""Edit existing blog post with valid changes"",
        ""preconditions"": [
          ""User is authenticated"",
          ""At least one existing post is listed on the Dashboard"",
          ""User is currently on the Dashboard page""
        ],
        ""steps"": [
          {
            ""action"": ""Click the Edit icon on an existing post card"",
            ""expectedResult"": ""User is navigated to the Edit Post page showing the existing post's data in the form""
          },
          {
            ""action"": ""Update the Title and/or post content"",
            ""expectedResult"": ""Fields reflect the changes made""
          },
          {
            ""action"": ""Click 'Save'"",
            ""expectedResult"": ""Changes are saved, and the user is returned to the Dashboard with updated post data displayed""
          }
        ],
        ""finalExpectedOutcome"": ""The post is successfully updated and shown with the revised Title or content in the Dashboard""
      },
      {
        ""title"": ""Edit blog post with missing mandatory Title (negative scenario)"",
        ""preconditions"": [
          ""User is authenticated"",
          ""At least one existing post is listed on the Dashboard"",
          ""User is currently on the Dashboard page""
        ],
        ""steps"": [
          {
            ""action"": ""Click the Edit icon on an existing post card"",
            ""expectedResult"": ""User is taken to the Edit Post form with pre-filled data""
          },
          {
            ""action"": ""Clear the Title field"",
            ""expectedResult"": ""Title field shows validation error indicating it is required""
          },
          {
            ""action"": ""Click 'Save'"",
            ""expectedResult"": ""The edit is blocked; validation error remains, and user stays on the Edit page""
          }
        ],
        ""finalExpectedOutcome"": ""User cannot save changes without providing a valid Title""
      },
      {
        ""title"": ""Delete blog post"",
        ""preconditions"": [
          ""User is authenticated"",
          ""At least one existing post is listed on the Dashboard""
        ],
        ""steps"": [
          {
            ""action"": ""Click the Delete icon on a post card"",
            ""expectedResult"": ""A confirmation prompt appears""
          },
          {
            ""action"": ""Confirm the deletion in the prompt"",
            ""expectedResult"": ""The post is removed from the Dashboard""
          }
        ],
        ""finalExpectedOutcome"": ""The selected post no longer appears on the Dashboard""
      },
      {
        ""title"": ""Cancel deletion of a blog post (negative scenario)"",
        ""preconditions"": [
          ""User is authenticated"",
          ""At least one existing post is listed on the Dashboard""
        ],
        ""steps"": [
          {
            ""action"": ""Click the Delete icon on a post card"",
            ""expectedResult"": ""A confirmation prompt appears""
          },
          {
            ""action"": ""Cancel the confirmation prompt"",
            ""expectedResult"": ""The deletion is aborted and the post remains in the list""
          }
        ],
        ""finalExpectedOutcome"": ""Post is not deleted if the user cancels at the confirmation prompt""
      },
      {
        ""title"": ""Preview a published blog post"",
        ""preconditions"": [
          ""User is authenticated"",
          ""At least one post with 'published' status is available"",
          ""User is on the Dashboard page""
        ],
        ""steps"": [
          {
            ""action"": ""Click on the title of a published post card"",
            ""expectedResult"": ""User is navigated to the Post Preview page showing rendered content""
          },
          {
            ""action"": ""Click 'Back' in the preview"",
            ""expectedResult"": ""User returns to the Dashboard page""
          }
        ],
        ""finalExpectedOutcome"": ""User can view published post content and then navigate back to the Dashboard""
      },
      {
        ""title"": ""Attempt to preview a draft post when user is not the owner (negative scenario)"",
        ""preconditions"": [
          ""User is authenticated but does not own the post"",
          ""A draft post belonging to another user exists"",
          ""User is on the Dashboard page""
        ],
        ""steps"": [
          {
            ""action"": ""Try accessing the preview link of the draft post"",
            ""expectedResult"": ""System detects user is not the owner and redirects them to the Dashboard""
          }
        ],
        ""finalExpectedOutcome"": ""User cannot preview a draft post they do not own; they remain on or return to the Dashboard""
      }
    ]
  }
]","[
  {
    ""app"": {
      ""name"": ""Simple CMS"",
      ""summary"": ""A basic Content Management System that allows users to create, edit, delete, and organize blog posts or articles with support for rich-text formatting and tags. Users can view all posts in a dashboard, manage them via forms, and publish or preview individual posts. Built with Next.js and Tailwind CSS for a modern, responsive experience.\n"",
      ""visual"": ""Light theme with primary color #2563eb (Tailwind blue-600). The overall look uses spacious card layouts, large font headings, and subtle gray backgrounds. Buttons and highlights use the primary color for clear calls to action. Components use rounded corners, medium padding, and the Inter font for readability.\n"",
      ""behaviour"": ""Smooth navigation between dashboard, create, edit, and preview pages using client-side routing. Loading states appear on API calls. Forms validate required fields before submission. Blog content updates instantly and supports drag-and-drop image uploads. Unsaved changes prompt before navigating away from forms.\n"",
      ""access"": ""Content Editors (authenticated users) can add, modify, or delete posts. Published post view is public.\n"",
      ""pages"": [
        {
          ""id"": ""dashboard"",
          ""title"": ""Dashboard"",
          ""purpose"": ""Show a list of existing blog posts/articles with actions to edit, delete, or create new posts. Entry point for content management.\n"",
          ""visual"": ""Centered, max-width layout with a 'New Post' button at top right. Posts are shown in card rows with title, status, tags, and edit/delete buttons. Header bar includes app title and user menu.\n"",
          ""behaviour"": ""Posts load from the backend API. Clicking edit navigates to the edit form, delete triggers a confirmation prompt, and 'New Post' navigates to the create form. Instant UI update on delete.\n"",
          ""sections"": [
            {
              ""id"": ""dashboard_header"",
              ""title"": ""Header"",
              ""visual"": ""Full-width horizontal bar with app logo/title on left and user avatar/menu on right. Primary color background, white text.\n"",
              ""behaviour"": ""Static navigation. User menu supports logout.\n"",
              ""blocks"": [
                {
                  ""id"": ""app_logo"",
                  ""visual"": ""Left-aligned icon and 'Simple CMS' text in bold, white font.\n"",
                  ""behaviour"": ""Navigates to dashboard.\n""
                },
                {
                  ""id"": ""user_avatar"",
                  ""visual"": ""Circular avatar image at 36px diameter. Click reveals dropdown menu.\n"",
                  ""behaviour"": ""Dropdown menu with logout option.\n""
                }
              ]
            },
            {
              ""id"": ""post_list"",
              ""title"": ""Post List"",
              ""visual"": ""Responsive grid of post cards (2–3 columns on wide screens, single column on mobile). Each card has title, status badge, tags, and action buttons.\n"",
              ""behaviour"": ""Loads posts from getPosts query. Edit and delete buttons shown for each post card.\n"",
              ""blocks"": [
                {
                  ""id"": ""new_post_button"",
                  ""visual"": ""Top right, blue 'New Post' button with plus icon. Rounded corners.\n"",
                  ""behaviour"": ""Navigates to create_post page.\n""
                },
                {
                  ""id"": ""post_card"",
                  ""visual"": ""White card, slight shadow, 24px padding, shows post title, tags as blue badges, status (draft/published) as a small badge, and edit/delete icons on the right.\n"",
                  ""behaviour"": ""Edit icon navigates to edit_post page, delete icon opens confirmation, clicking the title goes to public_preview page.\n""
                }
              ]
            }
          ]
        },
        {
          ""id"": ""create_post"",
          ""title"": ""Create New Post"",
          ""purpose"": ""Provide a form for users to author and submit new blog posts with rich text and tags.\n"",
          ""visual"": ""Centered card layout (max-width: 700px), large 'Create New Post' heading, form fields stacked vertically with gentle border and shadow. Primary color used for labels and action buttons.\n"",
          ""behaviour"": ""Form input validates title (required), content (required), and tags (optional, max 5). Rich text editor supports bold, italic, headings, bullet lists, links, and embeds. Unsaved changes warning appears on navigation. On submit, creates a new post and navigates to dashboard.\n"",
          ""sections"": [
            {
              ""id"": ""create_form_section"",
              ""title"": ""Post Form"",
              ""visual"": ""Vertically stacked fields: title (top), tags input, rich text editor, published checkbox, and action buttons below. Save and cancel side-by-side at bottom.\n"",
              ""behaviour"": ""Debounced validation on input. Save submits the form. Cancel prompts on unsaved changes.\n"",
              ""blocks"": [
                {
                  ""id"": ""post_title_input"",
                  ""visual"": ""Large text input with label 'Title', full width, blue border on focus.\n"",
                  ""behaviour"": ""Required, max 150 characters, shows error if blank.\n""
                },
                {
                  ""id"": ""tags_input"",
                  ""visual"": ""Multi-value input with tag badges; user can add up to 5 tags, each shown in blue pill with 'x' to remove.\n"",
                  ""behaviour"": ""Adds, removes, and limits tags client-side.\n""
                },
                {
                  ""id"": ""rich_text_editor"",
                  ""visual"": ""WYSIWYG editor with toolbar (bold, italic, headings, bullet/number list, link, image, code). Minimum 300px height.\n"",
                  ""behaviour"": ""Tracks input and triggers change state for validation and unsaved prompt.\n""
                },
                {
                  ""id"": ""published_checkbox"",
                  ""visual"": ""Inline toggle switch labeled 'Publish immediately?'.\n"",
                  ""behaviour"": ""Controls published field on submit.\n""
                },
                {
                  ""id"": ""form_actions"",
                  ""visual"": ""Row with two buttons: blue 'Save' and grey 'Cancel', spaced apart, both rounded.\n"",
                  ""behaviour"": ""Save validates and submits, cancel returns to dashboard with unsaved prompt if needed.\n""
                }
              ]
            }
          ]
        },
        {
          ""id"": ""edit_post"",
          ""title"": ""Edit Post"",
          ""purpose"": ""Let users modify existing blog posts, including content, tags, and published status.\n"",
          ""visual"": ""Centered card identical to create_post, but prepopulated with selected post data, and 'Edit Post' as heading.\n"",
          ""behaviour"": ""Loads post by id, allows same validation as create_post. Unsaved changes prompt, and Save updates post then returns to dashboard.\n"",
          ""sections"": [
            {
              ""id"": ""edit_form_section"",
              ""title"": ""Post Form"",
              ""visual"": ""Same vertical form as create_post section but pre-filled with chosen post. Heading shows 'Edit Post'.\n"",
              ""behaviour"": ""Same as create_form_section.\n"",
              ""blocks"": [
                {
                  ""id"": ""post_title_input"",
                  ""visual"": ""Large text input, labeled, with current post title, blue border on focus.\n"",
                  ""behaviour"": ""Required, max length, error if blank.\n""
                },
                {
                  ""id"": ""tags_input"",
                  ""visual"": ""Multi-value input with tag badges, pre-filled, add/remove option.\n"",
                  ""behaviour"": ""Adds, removes, and limits tags client-side.\n""
                },
                {
                  ""id"": ""rich_text_editor"",
                  ""visual"": ""WYSIWYG area showing the post's current content.\n"",
                  ""behaviour"": ""Editable, tracks changes to enable/disable save.\n""
                },
                {
                  ""id"": ""published_checkbox"",
                  ""visual"": ""Toggle switch for published state, reflects current value.\n"",
                  ""behaviour"": ""Updates published value.\n""
                },
                {
                  ""id"": ""form_actions"",
                  ""visual"": ""Row, blue 'Save' and grey 'Cancel' buttons. Save is disabled if no changes.\n"",
                  ""behaviour"": ""Save triggers update query, Cancel returns to dashboard with prompt on unsaved edits.\n""
                }
              ]
            }
          ]
        },
        {
          ""id"": ""public_preview"",
          ""title"": ""Post Preview"",
          ""purpose"": ""Display an individual blog post as it will appear to public readers. Used both for preview and published views; no editing available here.\n"",
          ""visual"": ""Centered, readable layout with large post title at top, tags beneath, metadata (author, date), and full-width rendered article below. Content area uses white background and generous vertical spacing, with styled headings, images, and lists. At top right, a back/home button.\n"",
          ""behaviour"": ""Loads post by id from public API, renders rich text safely. If post is draft and user is not the owner, redirect to dashboard.\n"",
          ""sections"": [
            {
              ""id"": ""post_header"",
              ""title"": ""Post Header"",
              ""visual"": ""Post title (extra-large, bold), row of tags as blue/gray badges, and metadata (date, author) in gray text below.\n"",
              ""behaviour"": ""Static content display only.\n"",
              ""blocks"": [
                {
                  ""id"": ""post_title_static"",
                  ""visual"": ""Extra-large black text heading.\n"",
                  ""behaviour"": ""Static.\n""
                },
                {
                  ""id"": ""post_tags_static"",
                  ""visual"": ""Row of pill-shaped tags colored blue/gray.\n"",
                  ""behaviour"": ""Static.\n""
                },
                {
                  ""id"": ""post_metadata_static"",
                  ""visual"": ""Smaller gray text: author name, date published.\n"",
                  ""behaviour"": ""Static.\n""
                }
              ]
            },
            {
              ""id"": ""post_body"",
              ""title"": ""Post Content"",
              ""visual"": ""Rendered rich-text content, using CMS styles for headings, images, links, lists, code, and blockquotes.\n"",
              ""behaviour"": ""No interactivity; safely rendered from stored content.\n""
            },
            {
              ""id"": ""preview_actions"",
              ""title"": ""Navigation"",
              ""visual"": ""Top right corner, simple 'Back' button styled as outlined secondary.\n"",
              ""behaviour"": ""Navigates to dashboard.\n""
            }
          ]
        },
        {
          ""id"": ""not_found"",
          ""title"": ""404 Not Found"",
          ""purpose"": ""Display a friendly message if a requested resource (post or page) is not found.\n"",
          ""visual"": ""Centered card with sad emoji, large '404' heading, and a button to return home.\n"",
          ""behaviour"": ""Static display.\n"",
          ""sections"": [
            {
              ""id"": ""not_found_section"",
              ""title"": ""Not Found Message"",
              ""visual"": ""Centered in viewport, emoji, heading, subheading, and button stacked vertically.\n"",
              ""behaviour"": ""Button links back to dashboard.\n"",
              ""blocks"": [
                {
                  ""id"": ""not_found_emoji"",
                  ""visual"": ""Large (48px) gray emoji.\n"",
                  ""behaviour"": ""Static.\n""
                },
                {
                  ""id"": ""not_found_heading"",
                  ""visual"": ""Black, bold 2xl heading '404 - Not Found'.\n"",
                  ""behaviour"": ""Static.\n""
                },
                {
                  ""id"": ""return_home_button"",
                  ""visual"": ""Blue primary button, rounded, says 'Return Home'.\n"",
                  ""behaviour"": ""Links to dashboard.\n""
                }
              ]
            }
          ]
        }
      ],
      ""queries"": [
        {
          ""id"": ""getPosts"",
          ""responsibility"": ""Fetches a list of all blog posts, including title, status (draft/published), tags, and metadata, for display on the dashboard.\n""
        },
        {
          ""id"": ""getPostById"",
          ""responsibility"": ""Fetches a single post (by id) with all details for editing or public viewing. Fails if not found or not public.\n""
        },
        {
          ""id"": ""createPost"",
          ""responsibility"": ""Creates a new blog post with title, content, tags, and published status as sent from the create form.\n""
        },
        {
          ""id"": ""updatePost"",
          ""responsibility"": ""Updates an existing blog post by id, modifying title, content, tags, or published status.\n""
        },
        {
          ""id"": ""deletePost"",
          ""responsibility"": ""Deletes a post by id after confirmation, removing it from the CMS.\n""
        },
        {
          ""id"": ""uploadImage"",
          ""responsibility"": ""Uploads an image and returns a URL for embedding in the rich text editor content.\n""
        }
      ]
    },
    ""mermaid"": ""graph TD\n  A(App: Simple CMS)\n  A --> B1[Dashboard Page]\n  A --> B2[Create Post Page]\n  A --> B3[Edit Post Page]\n  A --> B4[Public Preview Page]\n  A --> B5[Not Found Page]\n\n  subgraph Dashboard\n    B1a[Header Section]\n    B1b[Post List Section]\n    B1a --> B1a1[App Logo Block]\n    B1a --> B1a2[User Avatar Block]\n    B1b --> B1b1[New Post Button Block]\n    B1b --> B1b2[Post Card Block]\n  end\n  B1 --> B1a\n  B1 --> B1b\n\n  subgraph CreatePost\n    B2a[Create Form Section]\n    B2a --> B2a1[Title Input Block]\n    B2a --> B2a2[Tags Input Block]\n    B2a --> B2a3[Rich Text Editor Block]\n    B2a --> B2a4[Published Checkbox Block]\n    B2a --> B2a5[Form Actions Block]\n  end\n  B2 --> B2a\n\n  subgraph EditPost\n    B3a[Edit Form Section]\n    B3a --> B3a1[Title Input Block]\n    B3a --> B3a2[Tags Input Block]\n    B3a --> B3a3[Rich Text Editor Block]\n    B3a --> B3a4[Published Checkbox Block]\n    B3a --> B3a5[Form Actions Block]\n  end\n  B3 --> B3a\n\n  subgraph PostPreview\n    B4a[Header Section]\n    B4b[Body Section]\n    B4c[Navigation Section]\n    B4a --> B4a1[Post Title Static Block]\n    B4a --> B4a2[Post Tags Static Block]\n    B4a --> B4a3[Post Metadata Static Block]\n    B4b --> B4b1[Post Body Block]\n    B4c --> B4c1[Back Button Block]\n  end\n  B4 --> B4a\n  B4 --> B4b\n  B4 --> B4c\n\n  subgraph NotFound\n    B5a[Not Found Section]\n    B5a --> B5a1[Emoji Block]\n    B5a --> B5a2[Heading Block]\n    B5a --> B5a3[Return Home Button Block]\n  end\n  B5 --> B5a\n""
  }
]","[
  {
    ""metadata"": {
      ""name"": ""Simple CMS"",
      ""description"": ""A basic Content Management System that allows users to create, edit, delete, and organize blog posts or articles with support for rich-text formatting and tags"",
      ""version"": ""1.0.0"",
      ""generatedAt"": ""2024-01-15T10:30:00Z""
    },
    ""product"": {
      ""summary"": ""A basic Content Management System that allows users to create, edit, delete, and organize blog posts or articles with support for rich-text formatting and tags. Users can view all posts in a dashboard, manage them via forms, and publish or preview individual posts. Built with Next.js and Tailwind CSS for a modern, responsive experience."",
      ""goals"": [
        ""Enable content creators to easily manage blog posts and articles"",
        ""Provide rich-text editing capabilities with formatting options"",
        ""Support tagging and categorization of content"",
        ""Offer seamless publishing and preview functionality"",
        ""Deliver a modern, responsive user experience""
      ],
      ""personas"": [
        {
          ""name"": ""Content Editor"",
          ""description"": ""Authenticated users who create, edit, and manage blog posts"",
          ""goals"": [
            ""Create and publish engaging blog content"",
            ""Organize content with tags and categories"",
            ""Edit and update existing posts efficiently""
          ],
          ""painPoints"": [
            ""Complex content management interfaces"",
            ""Difficulty in formatting content properly"",
            ""Lack of preview functionality""
          ]
        }
      ],
      ""features"": [
        {
          ""name"": ""Post Management"",
          ""description"": ""Create, edit, delete, and organize blog posts"",
          ""priority"": ""High""
        },
        {
          ""name"": ""Rich Text Editor"",
          ""description"": ""WYSIWYG editor with formatting options"",
          ""priority"": ""High""
        },
        {
          ""name"": ""Tagging System"",
          ""description"": ""Add and manage tags for content organization"",
          ""priority"": ""Medium""
        },
        {
          ""name"": ""Publishing Control"",
          ""description"": ""Control post visibility and publication status"",
          ""priority"": ""High""
        },
        {
          ""name"": ""Image Upload"",
          ""description"": ""Upload and embed images in posts"",
          ""priority"": ""Medium""
        }
      ]
    },
    ""useCases"": [
      {
        ""id"": ""create_blog_post"",
        ""title"": ""Create Blog Post"",
        ""actor"": ""Content Editor"",
        ""description"": ""Content editor creates a new blog post with title, content, tags, and publishing options"",
        ""preconditions"": [
          ""User is authenticated"",
          ""User has content editor permissions""
        ],
        ""steps"": [
          ""Navigate to create post page"",
          ""Enter post title"",
          ""Add content using rich text editor"",
          ""Add relevant tags"",
          ""Set publishing status"",
          ""Submit post for creation""
        ],
        ""postconditions"": [
          ""New post is created in the system"",
          ""Post appears in dashboard""
        ]
      },
      {
        ""id"": ""edit_blog_post"",
        ""title"": ""Edit Blog Post"",
        ""actor"": ""Content Editor"",
        ""description"": ""Content editor modifies an existing blog post"",
        ""preconditions"": [
          ""User is authenticated"",
          ""Post exists in the system""
        ],
        ""steps"": [
          ""Navigate to edit post page"",
          ""Modify post content"",
          ""Update tags if needed"",
          ""Change publishing status if needed"",
          ""Save changes""
        ],
        ""postconditions"": [
          ""Post is updated with new information"",
          ""Changes are reflected in dashboard""
        ]
      },
      {
        ""id"": ""delete_blog_post"",
        ""title"": ""Delete Blog Post"",
        ""actor"": ""Content Editor"",
        ""description"": ""Content editor removes a blog post from the system"",
        ""preconditions"": [
          ""User is authenticated"",
          ""Post exists in the system""
        ],
        ""steps"": [
          ""Navigate to dashboard"",
          ""Select post to delete"",
          ""Confirm deletion""
        ],
        ""postconditions"": [
          ""Post is removed from the system"",
          ""Post no longer appears in dashboard""
        ]
      },
      {
        ""id"": ""preview_blog_post"",
        ""title"": ""Preview Blog Post"",
        ""actor"": ""Content Editor"",
        ""description"": ""Content editor previews how a blog post will appear to readers"",
        ""preconditions"": [
          ""Post exists in the system""
        ],
        ""steps"": [
          ""Navigate to post preview"",
          ""Review post content and formatting"",
          ""Return to dashboard or editing""
        ],
        ""postconditions"": null
      }
    ]
  }
]","[
    {
        ""testSuite"": {
            ""name"": ""CRM test suite"",
            ""description"": ""Validate navigation in the CRM app"",
            ""testCases"": [
                {
                    ""title"": ""Navigate Spec"",
                    ""precondition"": [
                        ""User has logged in""
                    ],
                    ""steps"": [
                        {
                            ""action"": ""Navigate to page1"",
                            ""expected"": """"
                        },
                        {
                            ""action"": ""Navigate to users"",
                            ""expected"": """"
                        }
                    ],
                    ""finalExpected"": ""User has successfully navigated to the page""
                },
                {
                    ""title"": ""Navigate Spec"",
                    ""precondition"": [
                        ""User has logged in""
                    ],
                    ""steps"": [
                        {
                            ""action"": ""Navigate to page1"",
                            ""expected"": """"
                        },
                        {
                            ""action"": ""Navigate to users"",
                            ""expected"": """"
                        }
                    ],
                    ""finalExpected"": ""User has successfully navigated to the page""
                }
            ]
        }
    }
]"
"[
  {
    ""testSuite"": {
      ""name"": ""BugTrackr Dashboard Test Suite"",
      ""description"": ""Functional test plan for the BugTrackr Dashboard user interface covering priority-based grouping, bug detail panel, real-time updates, and negative scenarios (UC: view_bugs_by_priority, view_bug_details, refresh_bug_data; UJ: daily_bug_triage)."",
      ""testCases"": [
        {
          ""title"": ""Load Dashboard Page (UC: view_bugs_by_priority)"",
          ""precondition"": [
            ""User is authenticated"",
            ""Bug data is available""
          ],
          ""steps"": [
            {
              ""action"": ""Navigate to the BugTrackr Dashboard URL"",
              ""expected"": ""Page loads with the top navigation bar, priority tabs, and a list of bug cards under the default priority""
            },
            {
              ""action"": ""Observe the real-time loading indicator"",
              ""expected"": ""Loading indicator is hidden once bug data is displayed""
            }
          ],
          ""finalExpected"": ""Dashboard is displayed with top navigation, priority tabs, and bug cards correctly loaded""
        },
        {
          ""title"": ""Filter Bugs by Priority (UC: view_bugs_by_priority)"",
          ""precondition"": [
            ""User is on the dashboard with bug data loaded""
          ],
          ""steps"": [
            {
              ""action"": ""Click the 'Critical' priority tab"",
              ""expected"": ""Only bugs with Critical priority are displayed""
            },
            {
              ""action"": ""Click the 'High' priority tab"",
              ""expected"": ""Only bugs with High priority are displayed""
            },
            {
              ""action"": ""Click the 'Medium' priority tab"",
              ""expected"": ""Only bugs with Medium priority are displayed""
            },
            {
              ""action"": ""Click the 'Low' priority tab"",
              ""expected"": ""Only bugs with Low priority are displayed""
            }
          ],
          ""finalExpected"": ""User can switch tabs to filter bugs by each priority group successfully""
        },
        {
          ""title"": ""Keyboard Navigation for Bug Selection"",
          ""precondition"": [
            ""User is on the dashboard with bug data loaded""
          ],
          ""steps"": [
            {
              ""action"": ""Focus on the bugs list using the keyboard"",
              ""expected"": ""The first bug card is highlighted""
            },
            {
              ""action"": ""Press the arrow down key"",
              ""expected"": ""The next bug card is highlighted""
            },
            {
              ""action"": ""Press the arrow up key"",
              ""expected"": ""The previous bug card is highlighted""
            },
            {
              ""action"": ""Press Enter on a highlighted bug card"",
              ""expected"": ""The detail panel opens showing that bug’s details""
            }
          ],
          ""finalExpected"": ""User can navigate through bug cards with arrow keys and open details using the Enter key""
        },
        {
          ""title"": ""View Bug Details (UC: view_bug_details)"",
          ""precondition"": [
            ""User is on the dashboard with bug data loaded""
          ],
          ""steps"": [
            {
              ""action"": ""Click on a specific bug card"",
              ""expected"": ""Bug detail panel slides in from the right""
            },
            {
              ""action"": ""Check the bug summary, ID, area, and priority"",
              ""expected"": ""All fields match the selected bug’s data""
            },
            {
              ""action"": ""Click the GitHub icon within the panel"",
              ""expected"": ""A new browser tab opens linking to the corresponding GitHub issue""
            },
            {
              ""action"": ""Expand 'Steps to Reproduce'"",
              ""expected"": ""Numbered list of reproduction steps is displayed""
            }
          ],
          ""finalExpected"": ""Bug detail panel shows correct information and includes a working GitHub link""
        },
        {
          ""title"": ""Close Bug Detail Panel"",
          ""precondition"": [
            ""An open bug detail panel""
          ],
          ""steps"": [
            {
              ""action"": ""Click the close (X) button at the top-right of the panel"",
              ""expected"": ""Bug detail panel slides out and is hidden""
            },
            {
              ""action"": ""Reopen a bug detail, then press the Escape key"",
              ""expected"": ""Bug detail panel closes when Escape is pressed""
            }
          ],
          ""finalExpected"": ""User can close the bug detail panel via the close button or ESC key""
        },
        {
          ""title"": ""Refresh Bug Data (UC: refresh_bug_data)"",
          ""precondition"": [
            ""User is on the dashboard""
          ],
          ""steps"": [
            {
              ""action"": ""Click the 'Refresh' button in the top navigation bar"",
              ""expected"": ""A loading indicator is displayed while fetching the latest bug data""
            },
            {
              ""action"": ""Observe the bug list after the loading indicator disappears"",
              ""expected"": ""Bug list updates to show the most recent bug data""
            }
          ],
          ""finalExpected"": ""User sees updated bug data with no loading indicator after the refresh completes""
        },
        {
          ""title"": ""Empty Bug List for a Priority (Negative Test)"",
          ""precondition"": [
            ""User is on the dashboard with bug data loaded""
          ],
          ""steps"": [
            {
              ""action"": ""Select a priority tab that contains no bugs"",
              ""expected"": ""The bugs list area is empty and displays no cards""
            }
          ],
          ""finalExpected"": ""User sees an empty state instead of bug cards if there are no bugs for the chosen priority""
        }
      ]
    }
  }
]","[
  {
    ""app"": {
      ""name"": ""BugTrackr Dashboard"",
      ""summary"": ""A dashboard for tracking software bugs grouped by priority. Each bug displays a summary, unique ID, application area, and a direct GitHub issue link. Clicking a bug reveals full details and steps to reproduce.\n"",
      ""visual"": ""Light theme, primary color #E4572E (orange-red). Uses a modern sans-serif font, with compact information density to display more rows per page. Priority groups are clearly color-coded. Layout is clear and focused on rapid bug triage.\n"",
      ""behaviour"": ""Real-time loading indicator on data operations. Client-side routing for dashboard and bug details. Refresh button updates bug data from the API. Bug details slide in from the right when selected, supporting fast switching.\n"",
      ""access"": ""All authenticated team members have read-only access. Update functionality is out of scope.\n"",
      ""pages"": [
        {
          ""id"": ""dashboard"",
          ""title"": ""Bug Tracking Dashboard"",
          ""purpose"": ""Show all bugs grouped by priority: Critical, High, Medium, and Low. Provide fast overview and access to full details for triage.\n"",
          ""visual"": ""Top navigation bar, then horizontal color tabs for each priority group. Below, a list of bug cards for the selected priority. Each card displays summary, ID, area, and a GitHub link. Right-side detail panel slides in when a bug is selected.\n"",
          ""behaviour"": ""Choosing a priority tab filters bug cards. Clicking a bug opens the detail panel. Bug list refreshes when the refresh button is pressed. Keyboard navigation supports up/down between bugs and enter to open details.\n"",
          ""sections"": [
            {
              ""id"": ""top_nav_section"",
              ""title"": ""Top Navigation"",
              ""visual"": ""Full-width, 56px navigation bar in primary color with white text. Left: app logo and title. Right: 'Refresh' button.\n"",
              ""behaviour"": ""Always visible, reloads current page. Refresh button triggers on-demand bug list reload.\n"",
              ""blocks"": [
                {
                  ""id"": ""nav_bar"",
                  ""visual"": ""Horizontal flex layout; logo and title left, refresh button right.\n"",
                  ""behaviour"": ""Refresh button triggers fetchBugs query.\n""
                }
              ]
            },
            {
              ""id"": ""priority_tabs_section"",
              ""title"": ""Priority Tabs"",
              ""visual"": ""Horizontal tab bar beneath navigation. Each tab is colored: Critical (red), High (orange), Medium (yellow), Low (green). Active tab is bold.\n"",
              ""behaviour"": ""Selecting a tab filters bug list by priority.\n"",
              ""blocks"": [
                {
                  ""id"": ""priority_tabs"",
                  ""visual"": ""Four horizontally arranged tabs with color highlight. Text bold for active tab.\n"",
                  ""behaviour"": ""Tab click updates displayed bug group.\n""
                }
              ]
            },
            {
              ""id"": ""bugs_list_section"",
              ""title"": ""Bugs List"",
              ""visual"": ""Card list with 8px spacing. Each card has summary (bold), ID (muted), area (chip-style), and GitHub icon button. Hover adds subtle shadow. Scrollable if overflow.\n"",
              ""behaviour"": ""Clicking a bug card opens right-side detail panel. Clicking GitHub link opens issue in new tab.\n"",
              ""blocks"": [
                {
                  ""id"": ""bug_card"",
                  ""visual"": ""350px wide card with 24px padding. Top: summary bold, beneath: ID, then area chip, right-aligned GitHub icon as button.\n"",
                  ""behaviour"": ""Click opens detail panel; GitHub icon opens link externally. Card highlights on hover and keyboard focus.\n""
                }
              ]
            },
            {
              ""id"": ""bug_detail_section"",
              ""title"": ""Bug Details Panel"",
              ""visual"": ""Right-docked panel, 480px wide, white background with drop shadow, scrollable. Shows full summary top, then table: ID, area, priority, GitHub link.  Below, expandable 'Steps to Reproduce' and additional metadata.\n"",
              ""behaviour"": ""Shows details of selected bug from list. Close button hides panel. Expanding/collapsing 'Steps to Reproduce' toggles step visibility. Pressing escape closes panel.\n"",
              ""blocks"": [
                {
                  ""id"": ""bug_summary_detail"",
                  ""visual"": ""H3 heading with full bug summary.\n"",
                  ""behaviour"": ""Static text.\n""
                },
                {
                  ""id"": ""id_area_priority_table"",
                  ""visual"": ""Table with two columns: field name left, value right. Shows ID, area, and priority. GitHub link as clickable icon.\n"",
                  ""behaviour"": ""GitHub icon opens issue in new tab.\n""
                },
                {
                  ""id"": ""steps_to_reproduce"",
                  ""visual"": ""Collapsible section labeled 'Steps to Reproduce'. Numbered list, default expanded.\n"",
                  ""behaviour"": ""Expand/collapse toggles step list. List fetched from bug details.\n""
                },
                {
                  ""id"": ""close_button"",
                  ""visual"": ""Icon button (X) at top-right corner.\n"",
                  ""behaviour"": ""Clicking or pressing Escape closes panel.\n""
                }
              ]
            }
          ]
        }
      ],
      ""queries"": [
        {
          ""id"": ""fetchBugs"",
          ""responsibility"": ""Fetches categorized bug records with summary, ID, area, priority, and GitHub link for all bugs, grouped by priority. Called on page load and when refresh is clicked.\n""
        },
        {
          ""id"": ""fetchBugDetails"",
          ""responsibility"": ""Fetches complete bug information and steps to reproduce for a given bug ID when card is selected.\n""
        }
      ]
    },
    ""mermaid"": ""graph LR\n  A[Top Navigation] --> B[Priority Tabs]\n  B --> C[Bugs List]\n  C --> D[Bug Detail Panel]\n  subgraph Dashboard Page\n    A\n    B\n    C\n    D\n  end\n  subgraph Bugs List\n    C1[Bug Card]\n    C --> C1\n  end\n  subgraph Bug Detail Panel\n    D1[Bug Summary]\n    D2[ID/Area/Priority Table]\n    D3[Steps to Reproduce]\n    D4[Close Button]\n    D --> D1\n    D --> D2\n    D --> D3\n    D --> D4\n  end\n""
  }
]","[
  {
    ""metadata"": {
      ""name"": ""BugTrackr Dashboard"",
      ""description"": ""A dashboard for tracking software bugs grouped by priority with real-time updates and detailed bug information"",
      ""version"": ""1.0.0"",
      ""generatedAt"": ""2024-01-15T10:30:00Z""
    },
    ""product"": {
      ""summary"": ""A dashboard for tracking software bugs grouped by priority. Each bug displays a summary, unique ID, application area, and a direct GitHub issue link. Clicking a bug reveals full details and steps to reproduce."",
      ""goals"": [
        ""Provide fast overview of bugs organized by priority levels"",
        ""Enable rapid bug triage with clear priority grouping"",
        ""Integrate with GitHub issues for seamless workflow"",
        ""Support real-time updates and efficient navigation""
      ],
      ""personas"": [
        {
          ""name"": ""Development Team Lead"",
          ""role"": ""Technical Lead"",
          ""goals"": [
            ""Quickly assess critical bugs requiring immediate attention"",
            ""Track bug distribution across application areas"",
            ""Coordinate team response to high-priority issues""
          ],
          ""painPoints"": [
            ""Difficulty prioritizing bugs across multiple projects"",
            ""Time wasted switching between bug tracking tools""
          ]
        },
        {
          ""name"": ""QA Engineer"",
          ""role"": ""Quality Assurance"",
          ""goals"": [
            ""Review bug details and reproduction steps"",
            ""Verify bug status and priority assignments"",
            ""Access GitHub issues for additional context""
          ],
          ""painPoints"": [
            ""Complex interfaces that slow down bug review"",
            ""Missing reproduction steps in bug reports""
          ]
        }
      ],
      ""features"": [
        {
          ""name"": ""Priority-Based Bug Grouping"",
          ""description"": ""Organize bugs into Critical, High, Medium, and Low priority categories"",
          ""priority"": ""High""
        },
        {
          ""name"": ""Bug Detail Panel"",
          ""description"": ""Slide-in panel showing complete bug information and reproduction steps"",
          ""priority"": ""High""
        },
        {
          ""name"": ""GitHub Integration"",
          ""description"": ""Direct links to GitHub issues for each bug"",
          ""priority"": ""Medium""
        },
        {
          ""name"": ""Real-time Updates"",
          ""description"": ""Refresh functionality to get latest bug data"",
          ""priority"": ""Medium""
        }
      ]
    },
    ""useCases"": [
      {
        ""id"": ""view_bugs_by_priority"",
        ""title"": ""View Bugs by Priority"",
        ""actor"": ""Development Team Member"",
        ""description"": ""User selects priority tab to view bugs grouped by Critical, High, Medium, or Low priority"",
        ""preconditions"": [
          ""User is authenticated"",
          ""Bug data is available""
        ],
        ""steps"": [
          ""User navigates to dashboard"",
          ""User clicks on priority tab (Critical, High, Medium, Low)"",
          ""System displays bugs filtered by selected priority"",
          ""User sees bug cards with summary, ID, area, and GitHub link""
        ],
        ""postconditions"": [
          ""Bug list is filtered by selected priority"",
          ""User can view relevant bugs for triage""
        ]
      },
      {
        ""id"": ""view_bug_details"",
        ""title"": ""View Bug Details"",
        ""actor"": ""Development Team Member"",
        ""description"": ""User clicks on bug card to view detailed information and reproduction steps"",
        ""preconditions"": [
          ""Bug list is displayed"",
          ""User has selected a bug card""
        ],
        ""steps"": [
          ""User clicks on bug card"",
          ""System opens detail panel from right side"",
          ""System fetches and displays complete bug information"",
          ""User reviews bug details, steps to reproduce, and metadata"",
          ""User can close panel or select different bug""
        ],
        ""postconditions"": [
          ""Bug detail panel is open with complete information"",
          ""User can access GitHub issue link""
        ]
      },
      {
        ""id"": ""refresh_bug_data"",
        ""title"": ""Refresh Bug Data"",
        ""actor"": ""Development Team Member"",
        ""description"": ""User refreshes bug data to get latest information"",
        ""preconditions"": [
          ""User is on dashboard""
        ],
        ""steps"": [
          ""User clicks refresh button in navigation"",
          ""System displays loading indicator"",
          ""System fetches latest bug data from API"",
          ""System updates bug list with new data""
        ],
        ""postconditions"": [
          ""Bug list shows most recent data"",
          ""Loading indicator is hidden""
        ]
      }
    ],
    ""userJourneys"": [
      {
        ""id"": ""daily_bug_triage"",
        ""title"": ""Daily Bug Triage""
      }
    ]
  }
]","[
    {
        ""testSuite"": {
            ""name"": ""CRM test suite"",
            ""description"": ""Validate navigation in the CRM app"",
            ""testCases"": [
                {
                    ""title"": ""Navigate Spec"",
                    ""precondition"": [
                        ""User has logged in""
                    ],
                    ""steps"": [
                        {
                            ""action"": ""Navigate to page1"",
                            ""expected"": """"
                        },
                        {
                            ""action"": ""Navigate to users"",
                            ""expected"": """"
                        }
                    ],
                    ""finalExpected"": ""User has successfully navigated to the page""
                },
                {
                    ""title"": ""Navigate Spec"",
                    ""precondition"": [
                        ""User has logged in""
                    ],
                    ""steps"": [
                        {
                            ""action"": ""Navigate to page1"",
                            ""expected"": """"
                        },
                        {
                            ""action"": ""Navigate to users"",
                            ""expected"": """"
                        }
                    ],
                    ""finalExpected"": ""User has successfully navigated to the page""
                }
            ]
        }
    }
]"
"[
  {
    ""testSuite"": {
      ""name"": ""Asset Tracker Test Suite"",
      ""description"": ""Functional test plan for the Asset Tracker application covering end-user UI scenarios based on PRD and TD."",
      ""testCases"": [
        {
          ""title"": ""Test 1: Verify initial Asset Dashboard load"",
          ""precondition"": [
            ""User is logged in"",
            ""User has permission to view Asset Dashboard""
          ],
          ""steps"": [
            {
              ""action"": ""Open application URL and navigate to Asset Dashboard"",
              ""expected"": ""Asset Dashboard loads with Header, Filter Bar, Alerts Panel, and Asset Table displayed. Existing Missing/Overdue alerts (if any) are visible.""
            }
          ],
          ""finalExpected"": ""The user sees the Asset Dashboard fully loaded with all relevant UI elements.""
        },
        {
          ""title"": ""Test 2: Verify navigation between Asset Dashboard and Maintenance History"",
          ""precondition"": [
            ""User is logged in"",
            ""User has permission to access both Asset Dashboard and Maintenance History""
          ],
          ""steps"": [
            {
              ""action"": ""From the Asset Dashboard, click the link or button to Maintenance History"",
              ""expected"": ""Maintenance History page is displayed.""
            },
            {
              ""action"": ""Use the browser back button"",
              ""expected"": ""Asset Dashboard page is displayed again and previous state is preserved.""
            }
          ],
          ""finalExpected"": ""The user can freely navigate between the two pages with correct state preservation.""
        },
        {
          ""title"": ""Test 3: Verify status filter updates table results on Asset Dashboard"",
          ""precondition"": [
            ""User is on the Asset Dashboard"",
            ""Multiple assets in different statuses exist""
          ],
          ""steps"": [
            {
              ""action"": ""Open the status dropdown in the Filter Bar and select 'Missing'"",
              ""expected"": ""The Asset Table shows only assets marked as Missing.""
            }
          ],
          ""finalExpected"": ""The user sees only Missing assets in the table after applying the status filter.""
        },
        {
          ""title"": ""Test 4: Verify user filter in Asset Dashboard"",
          ""precondition"": [
            ""User is on the Asset Dashboard"",
            ""At least two different users have assigned assets""
          ],
          ""steps"": [
            {
              ""action"": ""Type a valid user name in the User Filter autocomplete field"",
              ""expected"": ""The table updates to display only assets assigned to the entered user.""
            },
            {
              ""action"": ""Clear the user filter"",
              ""expected"": ""The table reverts to displaying all available assets.""
            }
          ],
          ""finalExpected"": ""User filter properly narrows down results and resets on clear.""
        },
        {
          ""title"": ""Test 5: Verify free-text search with debounce on Asset Dashboard"",
          ""precondition"": [
            ""User is on the Asset Dashboard"",
            ""Multiple assets exist with distinct names""
          ],
          ""steps"": [
            {
              ""action"": ""Enter partial asset name in the Search Box and wait 300ms"",
              ""expected"": ""Table results filter in real time after the debounce period, showing only matching assets.""
            },
            {
              ""action"": ""Clear the Search Box"",
              ""expected"": ""Table results revert to showing all assets.""
            }
          ],
          ""finalExpected"": ""Search functionality debounces user input properly and filters assets accordingly.""
        },
        {
          ""title"": ""Test 6: Verify no results found state in Search"",
          ""precondition"": [
            ""User is on the Asset Dashboard"",
            ""Multiple assets exist""
          ],
          ""steps"": [
            {
              ""action"": ""Enter a search term that matches no assets"",
              ""expected"": ""Table displays an empty state or 'No results found' message.""
            }
          ],
          ""finalExpected"": ""User sees a clear indication that no matching assets exist for the searched term.""
        },
        {
          ""title"": ""Test 7: Verify columns, sorting, and infinite scroll in Asset Table"",
          ""precondition"": [
            ""User is on the Asset Dashboard"",
            ""Enough assets exist to require multiple loads""
          ],
          ""steps"": [
            {
              ""action"": ""Scroll down the table to load more assets"",
              ""expected"": ""Additional rows load seamlessly (infinite scroll).""
            },
            {
              ""action"": ""Click on a column header (e.g., 'Status') to sort"",
              ""expected"": ""Table rows reorder based on the selected column in ascending or descending order.""
            }
          ],
          ""finalExpected"": ""Columns are visible, sorting is functional, and more assets load on scroll.""
        },
        {
          ""title"": ""Test 8: Verify empty state of Asset Table"",
          ""precondition"": [
            ""User is on the Asset Dashboard"",
            ""No assets exist or are assigned to the user""
          ],
          ""steps"": [
            {
              ""action"": ""Open the Asset Dashboard"",
              ""expected"": ""The table area shows an empty state or message indicating no assets to display.""
            }
          ],
          ""finalExpected"": ""User sees a clear empty state message informing them of no assets.""
        },
        {
          ""title"": ""Test 9: Verify 'Add Asset' button opens form modal"",
          ""precondition"": [
            ""User is logged in as Admin or Asset Manager"",
            ""User is on the Asset Dashboard""
          ],
          ""steps"": [
            {
              ""action"": ""Click the 'Add Asset' button in the Header section"",
              ""expected"": ""A slide-in modal appears with input fields for creating a new asset.""
            }
          ],
          ""finalExpected"": ""User can access the form modal to create a new asset.""
        },
        {
          ""title"": ""Test 10: Verify inline status update in Asset Table"",
          ""precondition"": [
            ""User is Admin, Asset Manager, or IT Staff with update permissions"",
            ""User is on the Asset Dashboard and viewing the Asset Table""
          ],
          ""steps"": [
            {
              ""action"": ""Click the status pill in a table row and change status to 'Missing'"",
              ""expected"": ""The row updates to show the red 'Missing' pill, and a missing alert appears in the Alerts Panel.""
            }
          ],
          ""finalExpected"": ""The status update is reflected in real time, and alert is triggered immediately.""
        },
        {
          ""title"": ""Test 11: Verify permission denial for Regular User updating an asset"",
          ""precondition"": [
            ""User is logged in as Regular User"",
            ""User is on the Asset Dashboard""
          ],
          ""steps"": [
            {
              ""action"": ""Attempt to click a status pill to change the asset status"",
              ""expected"": ""Operation is denied or unavailable, preventing the status change.""
            }
          ],
          ""finalExpected"": ""Regular User lacks permission and cannot update asset status.""
        },
        {
          ""title"": ""Test 12: Verify asset detail panel opens on row click"",
          ""precondition"": [
            ""User is on the Asset Dashboard"",
            ""Multiple assets exist""
          ],
          ""steps"": [
            {
              ""action"": ""Click on an asset row in the table"",
              ""expected"": ""A right-side slide-in panel appears, displaying the selected asset's detailed information.""
            }
          ],
          ""finalExpected"": ""User can view the Asset Detail panel with correct info for the selected asset.""
        },
        {
          ""title"": ""Test 13: Verify scheduling maintenance from detail panel"",
          ""precondition"": [
            ""User is Admin, Asset Manager, or IT Staff"",
            ""User is on the Asset Dashboard with an asset selected""
          ],
          ""steps"": [
            {
              ""action"": ""In the Asset Detail panel, click 'Schedule Maintenance' and enter a future date"",
              ""expected"": ""Maintenance schedule is updated; new date appears in the asset detail.""
            }
          ],
          ""finalExpected"": ""Maintenance scheduling is successfully applied and reflected in the asset detail view.""
        },
        {
          ""title"": ""Test 14: Verify missing alerts appear instantly upon marking asset missing"",
          ""precondition"": [
            ""User is on the Asset Dashboard"",
            ""At least one asset is not currently missing""
          ],
          ""steps"": [
            {
              ""action"": ""Mark a non-missing asset as 'Missing'"",
              ""expected"": ""A red alert pill for the asset appears in the Alerts Panel. The table row highlights accordingly.""
            }
          ],
          ""finalExpected"": ""System instantly updates the Alerts Panel with a new 'Missing' alert.""
        },
        {
          ""title"": ""Test 15: Verify overdue maintenance alerts on Asset Dashboard"",
          ""precondition"": [
            ""User is on the Asset Dashboard"",
            ""At least one asset has an overdue maintenance date""
          ],
          ""steps"": [
            {
              ""action"": ""Load the Asset Dashboard"",
              ""expected"": ""An orange/yellow pill for each overdue asset appears in the Alerts Panel.""
            }
          ],
          ""finalExpected"": ""Overdue maintenance alerts are clearly visible on the dashboard.""
        },
        {
          ""title"": ""Test 16: Verify maintenance history filters"",
          ""precondition"": [
            ""User is on the Maintenance History page"",
            ""Multiple maintenance records exist""
          ],
          ""steps"": [
            {
              ""action"": ""Set the Asset Name filter to a specific asset"",
              ""expected"": ""Table updates to show maintenance records for that asset only.""
            },
            {
              ""action"": ""Select a maintenance type, e.g. 'Preventive'"",
              ""expected"": ""Table further narrows to show only 'Preventive' records for the chosen asset.""
            }
          ],
          ""finalExpected"": ""Filters by asset name and maintenance type are applied correctly to the Maintenance History table.""
        },
        {
          ""title"": ""Test 17: Verify export functionality on Maintenance History"",
          ""precondition"": [
            ""User is on the Maintenance History page"",
            ""User has permission to export data""
          ],
          ""steps"": [
            {
              ""action"": ""Apply a date range filter to the records"",
              ""expected"": ""The table is filtered to only those records within the selected date range.""
            },
            {
              ""action"": ""Click the 'Export' button"",
              ""expected"": ""A CSV/XLSX file is downloaded containing only the currently filtered records.""
            }
          ],
          ""finalExpected"": ""User obtains a correct export of the filtered data from the Maintenance History page.""
        },
        {
          ""title"": ""Test 18: Verify access restriction for Maintenance History export by a Regular User"",
          ""precondition"": [
            ""User is logged in as Regular User"",
            ""User is on the Maintenance History page""
          ],
          ""steps"": [
            {
              ""action"": ""Attempt to click the 'Export' button in the Maintenance Header"",
              ""expected"": ""Export is disabled or an error message is shown, indicating insufficient permissions.""
            }
          ],
          ""finalExpected"": ""Regular User cannot export maintenance history data due to role-based restrictions.""
        }
      ]
    }
  }
]","[
  {
    ""app"": {
      ""name"": ""Asset Tracker"",
      ""summary"": ""A dashboard for organizations to manage and track assets, including assignment to users, flagging lost or unreturned items, and scheduling asset maintenance. Provides visibility into asset status, enables filtering, and generates alerts for missing or overdue assets.\n"",
      ""visual"": ""Light theme with primary color #2787F5, adopting a clean, modern, and spacious layout. Uses easy-to-read fonts with distinct secondary highlights for alerts and warnings.\n"",
      ""behaviour"": ""Real-time updates on asset assignment or status changes. Sticky notifications for urgent alerts (missing/overdue assets). Supports fast, client-side search and filtering with instant UI feedback. Responsive routing between dashboard and asset maintenance history views.\n"",
      ""access"": ""Admins, Asset Managers, and designated IT staff can view and manage asset data. Regular users can only see assets assigned to them.\n"",
      ""pages"": [
        {
          ""id"": ""asset_dashboard"",
          ""title"": ""Asset Dashboard"",
          ""purpose"": ""Central hub to view, manage, and monitor all organization assets. Allows filtering, searching, assigning to users, flagging assets as lost/unreturned, and surfaces key alerts for missing or overdue assets.\n"",
          ""visual"": ""Full-width page with header, horizontal filter bar, alerts panel at top for flagged items, asset table as main content, and a side panel for asset details/edits. Header has main navigation and quick add asset action.\n"",
          ""behaviour"": ""Supports fast search/filter. Clicking an asset opens side detail panel. Inline status and assignment updates. Alert banners animate into view for missing/overdue items. \n"",
          ""sections"": [
            {
              ""id"": ""header_section"",
              ""title"": ""Header"",
              ""visual"": ""Fixed top bar with #2787F5 background, white text, 56px height. Left side: app logo and title. Right side: action buttons (\""Add Asset\""). Below, breadcrumb or current page indicator.\n"",
              ""behaviour"": ""Always visible. \""Add Asset\"" triggers slide-in form modal.\n"",
              ""blocks"": [
                {
                  ""id"": ""nav_bar"",
                  ""visual"": ""Horizontal flex with logo, app title, and action buttons.\n"",
                  ""behaviour"": ""Handles navigation and opens the asset add modal.\n""
                }
              ]
            },
            {
              ""id"": ""alerts_panel"",
              ""title"": ""Alerts Panel"",
              ""visual"": ""Full-width banner above main table. Prominent red for 'Missing' alerts, orange/yellow for 'Overdue' maintenance. Each alert as a pill with asset name, status, and quick action icon.\n"",
              ""behaviour"": ""Shows when there are any assets flagged as missing or overdue. Alerts fade in/out. Click action jumps to asset row in table.\n"",
              ""blocks"": [
                {
                  ""id"": ""missing_alerts"",
                  ""visual"": ""Red-pilled badges for each missing asset, horizontally scrollable if too many.\n"",
                  ""behaviour"": ""Clicking badge scrolls table to relevant asset row.\n""
                },
                {
                  ""id"": ""overdue_maintenance_alerts"",
                  ""visual"": ""Yellow/orange-pilled badges for each overdue maintenance asset.\n"",
                  ""behaviour"": ""Same as missing alerts; jumps to asset in table.\n""
                }
              ]
            },
            {
              ""id"": ""filter_bar"",
              ""title"": ""Filter Bar"",
              ""visual"": ""Light grey toolbar below header with 16px padding and shadow. Contains filters for status (dropdown), user (autocomplete/typeahead), date range for last maintenance, and search box, all left-aligned in a row.\n"",
              ""behaviour"": ""Changing filters/search instantly updates table results.\n"",
              ""blocks"": [
                {
                  ""id"": ""status_filter"",
                  ""visual"": ""Dropdown listing statuses: All, Assigned, Unassigned, Missing, Overdue Maintenance.\n"",
                  ""behaviour"": ""Updates table based on selected status.\n""
                },
                {
                  ""id"": ""user_filter"",
                  ""visual"": ""User autocomplete/typeahead input with user avatars as options.\n"",
                  ""behaviour"": ""Filters table for assets assigned to selected user.\n""
                },
                {
                  ""id"": ""maintenance_date_filter"",
                  ""visual"": ""Date range picker input with calendar icon.\n"",
                  ""behaviour"": ""Filters assets by last maintenance date.\n""
                },
                {
                  ""id"": ""search_box"",
                  ""visual"": ""Full border text input with search icon prefix.\n"",
                  ""behaviour"": ""Free-text search by asset name or ID; debounced at 300ms.\n""
                }
              ]
            },
            {
              ""id"": ""asset_table_section"",
              ""title"": ""Asset Table"",
              ""visual"": ""Main content area, occupying most of the viewport. Table with sticky header and striped rows. Columns: Asset ID, Name, Status (colored pill), Assigned User (with avatar), Last Maintenance Date (calendar icon if overdue), and actions.\n"",
              ""behaviour"": ""Rows clickable to open side detail panel. Inline edit for assigned user and status. Sorting by each column.\n"",
              ""blocks"": [
                {
                  ""id"": ""asset_table"",
                  ""visual"": ""Wide, bordered table with 14px font, fixed column widths. Status column uses color-coded pills: blue = assigned, grey = unassigned, red = missing, yellow = overdue maintenance. Last Maintenance is red if overdue.\n"",
                  ""behaviour"": ""Clicking row opens asset detail. Inline controls update assignment/status and trigger update query. Supports sorting and infinite scroll.\n""
                }
              ]
            },
            {
              ""id"": ""asset_detail_panel"",
              ""title"": ""Asset Detail"",
              ""visual"": ""Right-side drawer/panel, overlays main view when open. White background, 24px padding, vertical layout. Header with asset name, close icon. Shows asset properties, action buttons below.\n"",
              ""behaviour"": ""Opens on row click. Allows editing asset info, assigning user, flagging as missing, scheduling maintenance. \""Save\"" persists changes, \""Schedule Maintenance\"" opens a modal to set a new date.\n"",
              ""blocks"": [
                {
                  ""id"": ""asset_properties"",
                  ""visual"": ""Vertical list: Asset ID, Name, Status, Assigned User (dropdown), Last Maintenance, Notes. Fields with appropriate icons/labels.\n"",
                  ""behaviour"": ""Editable fields bound to asset data. Inline save/cancel.\n""
                },
                {
                  ""id"": ""action_buttons"",
                  ""visual"": ""Row of buttons: \""Save\"", \""Flag Missing\"", \""Schedule Maintenance\"". Main color for primary, red for destructive, secondary for modals.\n"",
                  ""behaviour"": ""Triggers update, flag as missing, or opens schedule modal respectively.\n""
                }
              ]
            }
          ]
        },
        {
          ""id"": ""maintenance_history_page"",
          ""title"": ""Maintenance History"",
          ""purpose"": ""Shows a historical log of all maintenance performed on assets, with filters for date, asset, type of maintenance, and allows export/download of records.\n"",
          ""visual"": ""Table view with left-aligned filter controls above. Export and print buttons at the top right. Simple, list-style layout with date, asset name, maintenance type, performed by, and notes.\n"",
          ""behaviour"": ""Filters instantly update records. Export triggers CSV/XLSX download of visible records. Clicking a row shows detail modal with full maintenance notes.\n"",
          ""sections"": [
            {
              ""id"": ""maint_header"",
              ""title"": ""Maintenance Header"",
              ""visual"": ""Full-width bar matching app theme, with page title left, export/print right.\n"",
              ""behaviour"": ""Export button downloads current table data, print opens print dialog.\n"",
              ""blocks"": [
                {
                  ""id"": ""maint_title"",
                  ""visual"": ""H2 heading \""Maintenance History\"", bold, with history icon.\n"",
                  ""behaviour"": ""Static page label.\n""
                },
                {
                  ""id"": ""export_button"",
                  ""visual"": ""Blue outlined button with download icon.\n"",
                  ""behaviour"": ""Downloads filtered table as CSV/XLSX file.\n""
                },
                {
                  ""id"": ""print_button"",
                  ""visual"": ""Grey outlined button with printer icon.\n"",
                  ""behaviour"": ""Opens browser print dialog for table.\n""
                }
              ]
            },
            {
              ""id"": ""maint_filter_bar"",
              ""title"": ""Filter Bar"",
              ""visual"": ""Horizontal form with filters for asset name (autocomplete), date range (calendar picker), and maintenance type (dropdown).\n"",
              ""behaviour"": ""Filters update table instantly.\n"",
              ""blocks"": [
                {
                  ""id"": ""asset_name_filter"",
                  ""visual"": ""Asset autocomplete with device icon prefix.\n"",
                  ""behaviour"": ""Filters history by selected asset.\n""
                },
                {
                  ""id"": ""maintenance_type_filter"",
                  ""visual"": ""Simple dropdown: Preventive, Corrective, Inspection, Other.\n"",
                  ""behaviour"": ""Filters by selected maintenance type.\n""
                },
                {
                  ""id"": ""maint_date_filter"",
                  ""visual"": ""Date range picker as per dashboard.\n"",
                  ""behaviour"": ""Filters records by performed dates.\n""
                }
              ]
            },
            {
              ""id"": ""maint_history_table_section"",
              ""title"": ""Maintenance Table"",
              ""visual"": ""Table with striped rows, large row height for readability. Columns: Date, Asset Name, Maintenance Type, Performed By, Notes/Summary.\n"",
              ""behaviour"": ""Row click opens modal with full maintenance record notes.\n"",
              ""blocks"": [
                {
                  ""id"": ""maint_history_table"",
                  ""visual"": ""Table with sticky header, icons in type column, ellipsis for long notes.\n"",
                  ""behaviour"": ""Fetches and displays all maintenance events for filtered criteria. Row click opens detail view modal.\n""
                }
              ]
            }
          ]
        }
      ],
      ""queries"": [
        {
          ""id"": ""getAssets"",
          ""responsibility"": ""Fetches a list of all assets with their ID, name, status, assigned user, and last maintenance date. Supports filters for status, user, and date range, and search by name or ID.\n""
        },
        {
          ""id"": ""getAssetDetails"",
          ""responsibility"": ""Fetches detailed information on a single asset including history, assigned user, notes, and maintenance schedule.\n""
        },
        {
          ""id"": ""updateAsset"",
          ""responsibility"": ""Updates an asset with new assignment, status, maintenance schedule, or notes.\n""
        },
        {
          ""id"": ""getMissingAssets"",
          ""responsibility"": ""Returns a list of assets currently marked as missing or not returned by user.\n""
        },
        {
          ""id"": ""getOverdueMaintenance"",
          ""responsibility"": ""Lists assets whose scheduled maintenance date is overdue.\n""
        },
        {
          ""id"": ""getMaintenanceHistory"",
          ""responsibility"": ""Returns a history of all completed maintenance tasks for all assets, allowing filter by date, asset, and maintenance type.\n""
        },
        {
          ""id"": ""addAsset"",
          ""responsibility"": ""Creates a new asset and adds it to the system.\n""
        },
        {
          ""id"": ""scheduleMaintenance"",
          ""responsibility"": ""Schedules future maintenance for a specific asset.\n""
        }
      ]
    },
    ""mermaid_diagram"": ""graph TD\n  A[App: Asset Tracker]\n  A --> B[Page: Asset Dashboard]\n  A --> C[Page: Maintenance History]\n  B --> B1[Section: Header]\n  B --> B2[Section: Alerts Panel]\n  B --> B3[Section: Filter Bar]\n  B --> B4[Section: Asset Table]\n  B --> B5[Section: Asset Detail]\n  C --> C1[Section: Maintenance Header]\n  C --> C2[Section: Maintenance Filter Bar]\n  C --> C3[Section: Maintenance Table]\n  B1 --> B1a[Block: Nav Bar]\n  B2 --> B2a[Block: Missing Alerts]\n  B2 --> B2b[Block: Overdue Maintenance Alerts]\n  B3 --> B3a[Block: Status Filter]\n  B3 --> B3b[Block: User Filter]\n  B3 --> B3c[Block: Maintenance Date Filter]\n  B3 --> B3d[Block: Search Box]\n  B4 --> B4a[Block: Asset Table]\n  B5 --> B5a[Block: Asset Properties]\n  B5 --> B5b[Block: Action Buttons]\n  C1 --> C1a[Block: Maintenance Title]\n  C1 --> C1b[Block: Export Button]\n  C1 --> C1c[Block: Print Button]\n  C2 --> C2a[Block: Asset Name Filter]\n  C2 --> C2b[Block: Maintenance Type Filter]\n  C2 --> C2c[Block: Maintenance Date Filter]\n  C3 --> C3a[Block: Maintenance History Table]\n""
  }
]","[
  {
    ""metadata"": {
      ""name"": ""Asset Tracker"",
      ""description"": ""A dashboard for organizations to manage and track assets, including assignment to users, flagging lost or unreturned items, and scheduling asset maintenance"",
      ""version"": ""1.0.0"",
      ""generatedAt"": ""2024-01-15T10:00:00Z""
    },
    ""product"": {
      ""summary"": ""A dashboard for organizations to manage and track assets, including assignment to users, flagging lost or unreturned items, and scheduling asset maintenance. Provides visibility into asset status, enables filtering, and generates alerts for missing or overdue assets."",
      ""goals"": [
        ""Manage and track organizational assets effectively"",
        ""Enable asset assignment to users with real-time visibility"",
        ""Flag and monitor lost or unreturned items"",
        ""Schedule and track asset maintenance"",
        ""Generate alerts for missing or overdue assets"",
        ""Provide role-based access control for asset management""
      ],
      ""personas"": [
        {
          ""name"": ""Asset Manager"",
          ""role"": ""Primary user responsible for asset lifecycle management"",
          ""needs"": [
            ""Complete asset visibility"",
            ""Assignment tracking"",
            ""Maintenance scheduling"",
            ""Alert monitoring""
          ],
          ""permissions"": [
            ""Full asset CRUD"",
            ""User assignment"",
            ""Maintenance scheduling"",
            ""Alert management""
          ]
        },
        {
          ""name"": ""IT Staff"",
          ""role"": ""Technical staff managing IT assets and maintenance"",
          ""needs"": [
            ""Asset status updates"",
            ""Maintenance history"",
            ""Technical documentation"",
            ""Alert responses""
          ],
          ""permissions"": [
            ""Asset updates"",
            ""Maintenance records"",
            ""Status changes"",
            ""Technical notes""
          ]
        },
        {
          ""name"": ""Admin"",
          ""role"": ""System administrator with full access"",
          ""needs"": [
            ""System configuration"",
            ""User management"",
            ""Full asset control"",
            ""Reporting""
          ],
          ""permissions"": [
            ""Full system access"",
            ""User management"",
            ""Configuration"",
            ""All asset operations""
          ]
        },
        {
          ""name"": ""Regular User"",
          ""role"": ""Employee with assigned assets"",
          ""needs"": [
            ""View assigned assets"",
            ""Asset return notifications"",
            ""Basic asset info""
          ],
          ""permissions"": [
            ""View own assets"",
            ""Basic asset info"",
            ""Return confirmations""
          ]
        }
      ],
      ""features"": [
        {
          ""name"": ""Asset Dashboard"",
          ""description"": ""Central hub for asset management with real-time updates"",
          ""priority"": ""High"",
          ""complexity"": ""Medium""
        },
        {
          ""name"": ""Asset Assignment"",
          ""description"": ""Assign assets to users with tracking"",
          ""priority"": ""High"",
          ""complexity"": ""Medium""
        },
        {
          ""name"": ""Missing Asset Alerts"",
          ""description"": ""Flag and track missing or unreturned assets"",
          ""priority"": ""High"",
          ""complexity"": ""Low""
        },
        {
          ""name"": ""Maintenance Scheduling"",
          ""description"": ""Schedule and track asset maintenance"",
          ""priority"": ""Medium"",
          ""complexity"": ""Medium""
        },
        {
          ""name"": ""Maintenance History"",
          ""description"": ""Historical log of all maintenance activities"",
          ""priority"": ""Medium"",
          ""complexity"": ""Low""
        },
        {
          ""name"": ""Real-time Updates"",
          ""description"": ""Live updates on asset status changes"",
          ""priority"": ""Medium"",
          ""complexity"": ""High""
        },
        {
          ""name"": ""Search and Filtering"",
          ""description"": ""Fast search and filtering capabilities"",
          ""priority"": ""High"",
          ""complexity"": ""Low""
        },
        {
          ""name"": ""Role-based Access"",
          ""description"": ""Different access levels for different user roles"",
          ""priority"": ""Medium"",
          ""complexity"": ""Medium""
        }
      ]
    },
    ""useCases"": [
      {
        ""id"": ""manage_assets"",
        ""title"": ""Manage Assets"",
        ""description"": ""Asset managers can view, create, update, and delete assets"",
        ""actor"": ""Asset Manager"",
        ""preconditions"": [
          ""User is logged in"",
          ""User has asset management permissions""
        ],
        ""steps"": [
          ""Navigate to asset dashboard"",
          ""View list of all assets"",
          ""Filter or search for specific assets"",
          ""Create new asset or update existing"",
          ""Assign asset to user"",
          ""Update asset status""
        ],
        ""postconditions"": [
          ""Asset is updated in system"",
          ""Changes are reflected in dashboard""
        ],
        ""exceptions"": [
          ""Insufficient permissions"",
          ""Asset not found"",
          ""Invalid data""
        ]
      },
      {
        ""id"": ""track_missing_assets"",
        ""title"": ""Track Missing Assets"",
        ""description"": ""Flag assets as missing and monitor their status"",
        ""actor"": ""Asset Manager"",
        ""preconditions"": [
          ""Asset exists in system"",
          ""User has appropriate permissions""
        ],
        ""steps"": [
          ""Identify unreturned or missing asset"",
          null
        ]
      }
    ]
  }
]","[
    {
        ""testSuite"": {
            ""name"": ""CRM test suite"",
            ""description"": ""Validate navigation in the CRM app"",
            ""testCases"": [
                {
                    ""title"": ""Navigate Spec"",
                    ""precondition"": [
                        ""User has logged in""
                    ],
                    ""steps"": [
                        {
                            ""action"": ""Navigate to page1"",
                            ""expected"": """"
                        },
                        {
                            ""action"": ""Navigate to users"",
                            ""expected"": """"
                        }
                    ],
                    ""finalExpected"": ""User has successfully navigated to the page""
                },
                {
                    ""title"": ""Navigate Spec"",
                    ""precondition"": [
                        ""User has logged in""
                    ],
                    ""steps"": [
                        {
                            ""action"": ""Navigate to page1"",
                            ""expected"": """"
                        },
                        {
                            ""action"": ""Navigate to users"",
                            ""expected"": """"
                        }
                    ],
                    ""finalExpected"": ""User has successfully navigated to the page""
                }
            ]
        }
    }
]"
"[
  {
    ""testSuite"": {
      ""name"": ""Stock Vision Functional Test Suite"",
      ""description"": ""Comprehensive functional UI test suite covering core user journeys and requirements for the Stock Vision app."",
      ""testCases"": [
        {
          ""title"": ""Verify redirection to Login page for unauthenticated visitor (Negative)"",
          ""preconditions"": [
            ""No active user session""
          ],
          ""steps"": [
            {
              ""action"": ""Open the Dashboard URL without logging in"",
              ""expectedResult"": ""User is automatically redirected to the Login page""
            },
            {
              ""action"": ""Observe page content"",
              ""expectedResult"": ""Login page elements (username, password fields, login button, signup link) are visible""
            }
          ],
          ""finalExpectedOutcome"": ""Unauthenticated user cannot access the Dashboard; the Login page is displayed""
        },
        {
          ""title"": ""Verify successful login with valid credentials (User Authentication)"",
          ""preconditions"": [
            ""User is not logged in"",
            ""Valid username and password are available""
          ],
          ""steps"": [
            {
              ""action"": ""Navigate to Login page"",
              ""expectedResult"": ""Login page is displayed""
            },
            {
              ""action"": ""Enter valid username in the username field"",
              ""expectedResult"": ""Username field accepts input and no error message appears""
            },
            {
              ""action"": ""Enter valid password in the password field"",
              ""expectedResult"": ""Password field accepts input and no error message appears""
            },
            {
              ""action"": ""Click the Login button"",
              ""expectedResult"": ""Button is disabled briefly while processing, then redirects to the Dashboard""
            }
          ],
          ""finalExpectedOutcome"": ""User is redirected to the Dashboard and can see the watchlist interface""
        },
        {
          ""title"": ""Verify error on login with invalid credentials (Negative)"",
          ""preconditions"": [
            ""User is not logged in""
          ],
          ""steps"": [
            {
              ""action"": ""Navigate to Login page"",
              ""expectedResult"": ""Login page is displayed""
            },
            {
              ""action"": ""Enter invalid username or password"",
              ""expectedResult"": ""Invalid inputs are accepted in the fields""
            },
            {
              ""action"": ""Click the Login button"",
              ""expectedResult"": ""An error message is shown indicating invalid credentials; user remains on Login page""
            }
          ],
          ""finalExpectedOutcome"": ""User cannot log in with invalid credentials and is prompted with an error message""
        },
        {
          ""title"": ""Verify sign-up link navigation from the Login page"",
          ""preconditions"": [
            ""User is on the Login page""
          ],
          ""steps"": [
            {
              ""action"": ""Click on the Signup link below the Login button"",
              ""expectedResult"": ""System navigates to the Signup page""
            }
          ],
          ""finalExpectedOutcome"": ""User successfully navigates to the Signup page to create a new account""
        },
        {
          ""title"": ""Verify watchlist data is displayed on the Dashboard (UC-manage_watchlist)"",
          ""preconditions"": [
            ""User is logged in"",
            ""User has at least one stock in watchlist""
          ],
          ""steps"": [
            {
              ""action"": ""Navigate to the Dashboard"",
              ""expectedResult"": ""Dashboard is displayed with Top Bar and Watchlist Table sections""
            },
            {
              ""action"": ""Observe the watchlist table"",
              ""expectedResult"": ""Table displays symbol, name, price, daily change, trend, and remove button for each stock""
            }
          ],
          ""finalExpectedOutcome"": ""User sees all their watchlist stocks with accurate real-time or mock data in the table""
        },
        {
          ""title"": ""Verify empty watchlist state on the Dashboard (Negative)"",
          ""preconditions"": [
            ""User is logged in"",
            ""User's watchlist is cleared or has no stocks""
          ],
          ""steps"": [
            {
              ""action"": ""Navigate to the Dashboard with an empty watchlist"",
              ""expectedResult"": ""Dashboard shows an empty state message prompting the user to add stocks""
            }
          ],
          ""finalExpectedOutcome"": ""User sees a clear empty watchlist message and is prompted to add stocks""
        },
        {
          ""title"": ""Verify sorting functionality in Watchlist Table"",
          ""preconditions"": [
            ""User is logged in"",
            ""Watchlist has multiple stocks""
          ],
          ""steps"": [
            {
              ""action"": ""Click on the 'Symbol' column header"",
              ""expectedResult"": ""Rows re-order ascending by symbol""
            },
            {
              ""action"": ""Click 'Symbol' column header again"",
              ""expectedResult"": ""Rows re-order descending by symbol""
            }
          ],
          ""finalExpectedOutcome"": ""Stock rows respond correctly to user-initiated sorting""
        },
        {
          ""title"": ""Verify removing a stock from the watchlist directly on the Dashboard (UC-manage_watchlist)"",
          ""preconditions"": [
            ""User is logged in"",
            ""At least one stock exists in the watchlist""
          ],
          ""steps"": [
            {
              ""action"": ""Hover over a stock row in the watchlist table"",
              ""expectedResult"": ""A 'Remove' button is displayed""
            },
            {
              ""action"": ""Click the 'Remove' button"",
              ""expectedResult"": ""The stock is immediately removed from the table""
            }
          ],
          ""finalExpectedOutcome"": ""Selected stock is removed from the user's watchlist, and the table updates accordingly""
        },
        {
          ""title"": ""Verify searching for a stock and adding it to the watchlist (UC-search_add_stocks)"",
          ""preconditions"": [
            ""User is logged in"",
            ""User on the Search & Add page""
          ],
          ""steps"": [
            {
              ""action"": ""Enter a valid stock symbol or partial name in the search input"",
              ""expectedResult"": ""Autocomplete dropdown appears with up to 10 matching suggestions""
            },
            {
              ""action"": ""Press arrow keys or click on a suggestion"",
              ""expectedResult"": ""Selected stock's preview loads below with name, symbol, price, and daily change""
            },
            {
              ""action"": ""Click 'Add to Watchlist' button"",
              ""expectedResult"": ""'Add to Watchlist' button becomes disabled, system shows a confirmation or changes to 'Added'""
            }
          ],
          ""finalExpectedOutcome"": ""Stock is successfully added to the watchlist and user can confirm it in the Dashboard""
        },
        {
          ""title"": ""Verify debounced search and no results scenario (Negative)"",
          ""preconditions"": [
            ""User is logged in"",
            ""User on the Search & Add page""
          ],
          ""steps"": [
            {
              ""action"": ""Enter short or random text that does not match any stock symbol or name"",
              ""expectedResult"": ""Autocomplete yields no suggestions after debounce period""
            }
          ],
          ""finalExpectedOutcome"": ""User sees no suggestions and can conclude matching stocks do not exist""
        },
        {
          ""title"": ""Verify viewing stock Detail page from Dashboard (UC-view_stock_details)"",
          ""preconditions"": [
            ""User is logged in"",
            ""There is at least one stock in the watchlist""
          ],
          ""steps"": [
            {
              ""action"": ""Click on a stock row (excluding the Remove button) in the watchlist"",
              ""expectedResult"": ""User is navigated to the Stock Detail page""
            },
            {
              ""action"": ""Observe the detail header, mini chart, and metrics section"",
              ""expectedResult"": ""Page displays stock name, current price, daily change, day range, and trend icon""
            }
          ],
          ""finalExpectedOutcome"": ""User sees detailed stock information on the Stock Detail page""
        },
        {
          ""title"": ""Verify removing stock from the Stock Detail page (UC-view_stock_details)"",
          ""preconditions"": [
            ""User is logged in"",
            ""Stock Detail page is shown for a watchlist stock""
          ],
          ""steps"": [
            {
              ""action"": ""Click the 'Remove' button in the Stock Metrics section"",
              ""expectedResult"": ""Button disables; user is routed back to the Dashboard""
            },
            {
              ""action"": ""Check watchlist table"",
              ""expectedResult"": ""Removed stock no longer appears in the watchlist""
            }
          ],
          ""finalExpectedOutcome"": ""Stock is successfully removed from watchlist, and user is returned to Dashboard""
        },
        {
          ""title"": ""Verify real-time price updates on the Dashboard (Real-time Stock Data)"",
          ""preconditions"": [
            ""User is logged in"",
            ""User has at least one stock in the watchlist""
          ],
          ""steps"": [
            {
              ""action"": ""Remain on the Dashboard for over 30 seconds"",
              ""expectedResult"": ""Stock prices and daily changes automatically refresh without manual reload""
            }
          ],
          ""finalExpectedOutcome"": ""User sees updated stock data at regular 30-second intervals""
        },
        {
          ""title"": ""Verify real-time mini chart updates on the Stock Detail page"",
          ""preconditions"": [
            ""User is logged in"",
            ""User navigates to a specific Stock Detail page""
          ],
          ""steps"": [
            {
              ""action"": ""Observe mini chart for over 30 seconds"",
              ""expectedResult"": ""Chart refreshes with updated data points or trend lines""
            }
          ],
          ""finalExpectedOutcome"": ""User sees the mini chart seamlessly reflect new data without leaving the page""
        },
        {
          ""title"": ""Verify navigating back to Dashboard from Stock Detail"",
          ""preconditions"": [
            ""User is logged in"",
            ""User is on a Stock Detail page""
          ],
          ""steps"": [
            {
              ""action"": ""Click the 'Back Arrow' icon in the Detail Header"",
              ""expectedResult"": ""App navigates back to the Dashboard""
            }
          ],
          ""finalExpectedOutcome"": ""User successfully returns to the Dashboard watchlist view""
        }
      ]
    }
  }
]","[
  {
    ""app"": {
      ""name"": ""Stock Vision"",
      ""summary"": ""A stock watchlist app enabling users to search for stocks, add them to a personalized watchlist, and monitor real-time or mock stock data such as current price, daily change, and trend indicators. Users can manage their watchlist through a dashboard, search and add new stocks with autocomplete, and view detailed stock info with a mini price chart.\n"",
      ""visual"": ""Uses a modern light theme in Next.js and Tailwind CSS. The primary palette is shades of grey (#F3F4F6, #D1D5DB, #6B7280, #111827) combined with violet highlights (#7C3AED, #A78BFA). Components are spaced for clarity, with a medium information density and rounded corners. The UI is clean, with clear hierarchy and sectioning.\n"",
      ""behaviour"": ""Stocks update in real-time or from a mock data provider at regular intervals (e.g., every 30s). Routing is client-side and seamless via Next.js. Sorting, searching, and adding stocks are all instant and debounced for smooth user experience. Entry point is the dashboard for authenticated users.\n"",
      ""access"": ""Registered users only may access the app and manage their watchlists. Public visitors are redirected to a login/signup page.\n"",
      ""pages"": [
        {
          ""id"": ""login"",
          ""title"": ""Login"",
          ""purpose"": ""Allow users to authenticate to access and manage their personalized stock watchlist.\n"",
          ""visual"": ""Centered login card in a full-height page with a grey gradient background and subtle violet accent. Input fields are vertically stacked with prominent login and signup buttons. Form is compact and readable.\n"",
          ""behaviour"": ""On valid login, redirect to dashboard. Form validation for empty or invalid fields. Option to go to signup.\n"",
          ""sections"": [
            {
              ""id"": ""login_card"",
              ""title"": ""Login Card"",
              ""visual"": ""Card with white background (#FFF), violet top border, 24px padding, 16px corner radius. Vertically stacked items: username/email input, password input, a login button, a grey text link for signup.\n"",
              ""behaviour"": ""Handles input validation, shows error states, and disables the button while processing.\n"",
              ""blocks"": [
                {
                  ""id"": ""username_input"",
                  ""visual"": ""Full-width input with light grey border, rounded corners, violet focus ring.\n"",
                  ""behaviour"": ""Accepts user's email or username. Validation on blur.\n""
                },
                {
                  ""id"": ""password_input"",
                  ""visual"": ""Full-width password input styled as above.\n"",
                  ""behaviour"": ""Accepts password. \""Show/hide password\"" toggle on right.\n""
                },
                {
                  ""id"": ""login_button"",
                  ""visual"": ""Wide button, violet background (#7C3AED), white bold text. Rounded, with hover darkening.\n"",
                  ""behaviour"": ""Submits the login form, disables on submission.\n""
                },
                {
                  ""id"": ""signup_link"",
                  ""visual"": ""Small, center-aligned, grey text link below the login button.\n"",
                  ""behaviour"": ""Navigates to signup page.\n""
                }
              ]
            }
          ]
        },
        {
          ""id"": ""dashboard"",
          ""title"": ""Watchlist Dashboard"",
          ""purpose"": ""Present a sortable, searchable table of all stocks in the user's watchlist, with real-time (or mock) market data, allowing for quick monitoring and management.\n"",
          ""visual"": ""Full-width, single-column layout. Top bar with app title, user avatar, and a violet \""Add Stock\"" button. Main panel is a white card containing a table with sticky header, zebra striping, and sortable columns. Table header has bold text, columns for symbol, name, current price, daily change, and trend indicator.\n"",
          ""behaviour"": ""Live updates every 30s for stock prices and trends. Clicking columns sorts ascending/descending. \""Add Stock\"" button navigates to the search/add page. Clicking a stock row opens the detail view. Table supports inline removal of a stock.\n"",
          ""sections"": [
            {
              ""id"": ""dashboard_header"",
              ""title"": ""Top Bar"",
              ""visual"": ""Grey (#F3F4F6) rectangular bar, app icon/heading on the left, user avatar right, \""Add Stock\"" violet button at far right.\n"",
              ""behaviour"": ""The \""Add Stock\"" button routes to the search page. Avatar opens a simple dropdown with log out.\n"",
              ""blocks"": [
                {
                  ""id"": ""app_title"",
                  ""visual"": ""Bold, large font, left-aligned, violet accent for part of the name.\n"",
                  ""behaviour"": ""Static display.\n""
                },
                {
                  ""id"": ""user_avatar"",
                  ""visual"": ""Circular avatar, small shadow, at the right edge.\n"",
                  ""behaviour"": ""Opens menu with \""Logout\"".\n""
                },
                {
                  ""id"": ""add_stock_button"",
                  ""visual"": ""Small, pill-shaped button, violet gradient, white text.\n"",
                  ""behaviour"": ""Navigates to search_and_add page.\n""
                }
              ]
            },
            {
              ""id"": ""watchlist_table_section"",
              ""title"": ""Watchlist Table"",
              ""visual"": ""White background card with subtle shadow and 24px radius, inner 16px padding. Table's header row is light grey (#F3F4F6), sticky on scroll. Table fills width and is horizontally scrollable on mobile.\n"",
              ""behaviour"": ""Each row is clickable. Hover highlights in violet. The 'Remove' button shows on row hover.\n"",
              ""blocks"": [
                {
                  ""id"": ""watchlist_table"",
                  ""visual"": ""Table with headers: Symbol, Name, Price, Daily Change, Trend, Remove. Text: 16px, semibold headers. Prices bold, color-coded: green for up, red for down. Mini sparkline icon under trend column.\n"",
                  ""behaviour"": ""Fetches data from getWatchlistStocks query. Sortable by each column header. Clicking a row (excluding Remove) routes to detail. Remove deletes the stock from the user's watchlist.\n""
                },
                {
                  ""id"": ""empty_state"",
                  ""visual"": ""Centered, grey text with icon, soft violet background.\n"",
                  ""behaviour"": ""Displayed if watchlist is empty, prompts user to add stocks.\n""
                }
              ]
            }
          ]
        },
        {
          ""id"": ""search_and_add"",
          ""title"": ""Search & Add Stocks"",
          ""purpose"": ""Allow users to efficiently search for any US/major global stock, view quick info with autocomplete, and add the stock to their watchlist.\n"",
          ""visual"": ""Single-centered card with white background on a pale violet background. Includes large search input with violet shadow, below it an autocomplete drop-down styled with zebra stripes and highlighted on arrow key navigation. Info preview for selected suggestion.\n"",
          ""behaviour"": ""Search bar is debounced, with dropdown suggesting stocks. Arrow up/down navigates suggestions. Clicking or pressing enter on suggestion shows details below, with an \""Add to Watchlist\"" violet button. If already in watchlist, show \""Added\"" state and disable button.\n"",
          ""sections"": [
            {
              ""id"": ""search_section"",
              ""title"": ""Stock Search"",
              ""visual"": ""Vertically stacked layout: search box at top, suggestion dropdown below. Suggestions are in a fixed-height white box with overflow scroll, separated by subtle grey dividers. Each suggestion: symbol, company name, faded price.\n"",
              ""behaviour"": ""Debounced search triggers getStockSuggestions. Keyboard and click navigation supported.\n"",
              ""blocks"": [
                {
                  ""id"": ""search_input"",
                  ""visual"": ""Large input field, violet glow shadow on focus, search icon inside.\n"",
                  ""behaviour"": ""On change, debounced to trigger search suggestion fetch.\n""
                },
                {
                  ""id"": ""autocomplete_list"",
                  ""visual"": ""Dropdown below input. Each item has a grey background on highlight, left-aligned symbol and company, right-aligned small price.\n"",
                  ""behaviour"": ""Arrow keys and mouse select items, enter/click loads preview below.\n""
                }
              ]
            },
            {
              ""id"": ""add_panel"",
              ""title"": ""Add Panel"",
              ""visual"": ""Card below search displaying selected stock's quick info: company name, symbol, mini chart (sparklines), current price in bold, daily change with up/down icon. Centered violet \""Add to Watchlist\"" button below.\n"",
              ""behaviour"": ""Clicking \""Add\"" adds stock to watchlist, disables button, and shows confirmation.\n""
            }
          ]
        },
        {
          ""id"": ""stock_detail"",
          ""title"": ""Stock Detail View"",
          ""purpose"": ""Offer a focused view for a selected stock, with detailed data, a mini chart, and quick actions.\n"",
          ""visual"": ""White card, 32px radius, elevated with shadow. Top bar with back arrow and name/symbol, then mini chart spanning the width, then two columns: left for info/metrics (current price, daily change, daily high/low), right for trend stats and \""Remove from Watchlist\"" button (violet). Metrics use bold, color-coded (green up, red down, grey neutral). Card is horizontally and vertically centered.\n"",
          ""behaviour"": ""Stock data fetched live or from mock API. \""Remove\"" instantly deletes from watchlist and routes back to dashboard. Page auto-refreshes data every 30s.\n"",
          ""sections"": [
            {
              ""id"": ""detail_header"",
              ""title"": ""Detail Header"",
              ""visual"": ""Left-aligned back arrow, right-aligned large bold name/symbol, soft violet border at bottom.\n"",
              ""behaviour"": ""Back arrow routes back to dashboard.\n"",
              ""blocks"": [
                {
                  ""id"": ""back_arrow"",
                  ""visual"": ""Icon button, rounded, grey hover effect.\n"",
                  ""behaviour"": ""On click, navigates back.\n""
                },
                {
                  ""id"": ""stock_name"",
                  ""visual"": ""Bold, large text. Symbol smaller next to name. Violet accent.\n"",
                  ""behaviour"": ""Static display.\n""
                }
              ]
            },
            {
              ""id"": ""stock_chart_section"",
              ""title"": ""Mini Chart"",
              ""visual"": ""Top third of the card: Mini line chart, violet line, background grid in faint grey.\n"",
              ""behaviour"": ""Renders using price/time data from getStockDetail. Animates on new data.\n"",
              ""blocks"": [
                {
                  ""id"": ""mini_line_chart"",
                  ""visual"": ""Responsive SVG or canvas chart, violet line, data points marked with small dots.\n"",
                  ""behaviour"": ""Re-renders on live data refresh.\n""
                }
              ]
            },
            {
              ""id"": ""detail_metrics_section"",
              ""title"": ""Stock Metrics"",
              ""visual"": ""2-column grid, first for prices/changes, second for trend indicators and action button. Numbers large and clear, up/down icons colored green/red.\n"",
              ""behaviour"": ""Updates every data poll. Remove button disables/greys on click.\n"",
              ""blocks"": [
                {
                  ""id"": ""current_price"",
                  ""visual"": ""Large, bold, color-coded number.\n"",
                  ""behaviour"": ""Updates with current price.\n""
                },
                {
                  ""id"": ""daily_change"",
                  ""visual"": ""Medium-sized number, up/down icon, green/red depending on change.\n"",
                  ""behaviour"": ""Updates with each refresh.\n""
                },
                {
                  ""id"": ""day_range"",
                  ""visual"": ""Text for high and low. Small, muted grey.\n"",
                  ""behaviour"": ""Static, based on day's data.\n""
                },
                {
                  ""id"": ""trend_icon"",
                  ""visual"": ""Up/down arrow inside filled circle, color-coded.\n"",
                  ""behaviour"": ""Represents 7-day trend from fetched data.\n""
                },
                {
                  ""id"": ""remove_button"",
                  ""visual"": ""Right-aligned, violet outline button, red on hover, rounded.\n"",
                  ""behaviour"": ""Removes the stock from watchlist, disables and routes to dashboard.\n""
                }
              ]
            }
          ]
        }
      ],
      ""queries"": [
        {
          ""id"": ""getWatchlistStocks"",
          ""responsibility"": ""Returns the list of stocks in the current user's watchlist, each with real-time or mock data: symbol, name, current price, daily change %, daily chart data, and 7-day trend.\n""
        },
        {
          ""id"": ""getStockSuggestions"",
          ""responsibility"": ""Returns up to 10 best-matching stocks based on partial symbol or company name input, including symbol, name, and current price for search autocomplete.\n""
        },
        {
          ""id"": ""addStockToWatchlist"",
          ""responsibility"": ""Adds a given stock (by symbol) to the authenticated user's watchlist. No-op if already present.\n""
        },
        {
          ""id"": ""removeStockFromWatchlist"",
          ""responsibility"": ""Removes a stock (by symbol) from the user's watchlist.\n""
        },
        {
          ""id"": ""getStockDetail"",
          ""responsibility"": ""Fetches detailed info on a single stock, including intraday/minute price data, current price, daily range, and recent trend, by symbol.\n""
        }
      ]
    },
    ""mermaid"": ""graph TD\n  App[Stock Vision App]\n  App --> LoginPage\n  App --> Dashboard\n  App --> SearchAddPage\n  App --> StockDetailPage\n  LoginPage[Login Page]\n  LoginPage --> LoginCard\n  LoginCard --> UsernameInput\n  LoginCard --> PasswordInput\n  LoginCard --> LoginButton\n  LoginCard --> SignupLink\n  Dashboard[Watchlist Dashboard]\n  Dashboard --> DashboardHeader\n  DashboardHeader --> AppTitle\n  DashboardHeader --> UserAvatar\n  DashboardHeader --> AddStockButton\n  Dashboard --> WatchlistTableSection\n  WatchlistTableSection --> WatchlistTable\n  WatchlistTableSection --> EmptyState\n  SearchAddPage[Search & Add Stocks]\n  SearchAddPage --> SearchSection\n  SearchSection --> SearchInput\n  SearchSection --> AutocompleteList\n  SearchAddPage --> AddPanel\n  AddPanel --> StockQuickInfo\n  AddPanel --> AddToWatchlistButton\n  StockDetailPage[Stock Detail View]\n  StockDetailPage --> DetailHeader\n  DetailHeader --> BackArrow\n  DetailHeader --> StockName\n  StockDetailPage --> StockChartSection\n  StockChartSection --> MiniLineChart\n  StockDetailPage --> DetailMetricsSection\n  DetailMetricsSection --> CurrentPrice\n  DetailMetricsSection --> DailyChange\n  DetailMetricsSection --> DayRange\n  DetailMetricsSection --> TrendIcon\n  DetailMetricsSection --> RemoveButton\n""
  }
]","[
  {
    ""metadata"": {
      ""name"": ""Stock Vision"",
      ""description"": ""A stock watchlist app enabling users to search for stocks, add them to a personalized watchlist, and monitor real-time or mock stock data such as current price, daily change, and trend indicators."",
      ""version"": ""1.0.0"",
      ""generatedAt"": ""2024-12-19T10:30:00Z""
    },
    ""product"": {
      ""summary"": ""A stock watchlist app enabling users to search for stocks, add them to a personalized watchlist, and monitor real-time or mock stock data such as current price, daily change, and trend indicators. Users can manage their watchlist through a dashboard, search and add new stocks with autocomplete, and view detailed stock info with a mini price chart."",
      ""goals"": [
        ""Enable users to create and manage personalized stock watchlists"",
        ""Provide real-time or mock stock data monitoring"",
        ""Offer intuitive search and autocomplete functionality for stock discovery"",
        ""Display detailed stock information with visual price charts"",
        ""Ensure secure access through user authentication""
      ],
      ""personas"": [
        {
          ""name"": ""Active Stock Trader"",
          ""description"": ""Experienced trader who monitors multiple stocks daily"",
          ""goals"": [
            ""Track multiple stocks simultaneously"",
            ""Get real-time price updates"",
            ""Quickly add and remove stocks from watchlist""
          ],
          ""painPoints"": [
            ""Needs fast, reliable data updates"",
            ""Wants clean, organized interface""
          ]
        },
        {
          ""name"": ""Casual Investor"",
          ""description"": ""Part-time investor who checks stocks periodically"",
          ""goals"": [
            ""Monitor a few selected stocks"",
            ""Understand daily price movements"",
            ""Easy stock search and management""
          ],
          ""painPoints"": [
            ""Overwhelmed by complex interfaces"",
            ""Needs simple, clear information""
          ]
        }
      ],
      ""features"": [
        {
          ""name"": ""Stock Watchlist Management"",
          ""description"": ""Add, remove, and organize stocks in personal watchlist"",
          ""priority"": ""high""
        },
        {
          ""name"": ""Real-time Stock Data"",
          ""description"": ""Live price updates and trend indicators"",
          ""priority"": ""high""
        },
        {
          ""name"": ""Stock Search and Autocomplete"",
          ""description"": ""Search for stocks with intelligent suggestions"",
          ""priority"": ""high""
        },
        {
          ""name"": ""Stock Detail View"",
          ""description"": ""Detailed information with mini price charts"",
          ""priority"": ""medium""
        },
        {
          ""name"": ""User Authentication"",
          ""description"": ""Secure login and personalized experience"",
          ""priority"": ""high""
        }
      ]
    },
    ""useCases"": [
      {
        ""id"": ""manage_watchlist"",
        ""title"": ""Manage Stock Watchlist"",
        ""actor"": ""Authenticated User"",
        ""description"": ""User can view, add, and remove stocks from their personal watchlist"",
        ""preconditions"": [
          ""User is authenticated""
        ],
        ""steps"": [
          ""User accesses dashboard"",
          ""User views current watchlist"",
          ""User can sort and filter stocks"",
          ""User can remove stocks from watchlist""
        ],
        ""postconditions"": [
          ""Watchlist is updated in real-time""
        ]
      },
      {
        ""id"": ""search_add_stocks"",
        ""title"": ""Search and Add Stocks"",
        ""actor"": ""Authenticated User"",
        ""description"": ""User can search for stocks and add them to watchlist"",
        ""preconditions"": [
          ""User is authenticated""
        ],
        ""steps"": [
          ""User navigates to search page"",
          ""User enters stock symbol or company name"",
          ""System provides autocomplete suggestions"",
          ""User selects stock and views preview"",
          ""User adds stock to watchlist""
        ],
        ""postconditions"": [
          ""Stock is added to user's watchlist""
        ]
      },
      {
        ""id"": ""view_stock_details"",
        ""title"": ""View Stock Details"",
        ""actor"": ""Authenticated User"",
        ""description"": ""User can view detailed information about a specific stock"",
        ""preconditions"": [
          ""User is authenticated"",
          ""Stock exists in watchlist""
        ],
        ""steps"": [
          ""User clicks on stock from watchlist"",
          ""User views detailed stock information"",
          ""User sees mini price chart"",
          ""User can remove stock from detail view""
        ],
        ""postconditions"": [
          ""User has detailed stock information""
        ]
      }
    ],
    ""userJourneys"": [
      {
        ""id"": ""new_user_onboarding""
      }
    ]
  }
]","[
    {
        ""testSuite"": {
            ""name"": ""CRM test suite"",
            ""description"": ""Validate navigation in the CRM app"",
            ""testCases"": [
                {
                    ""title"": ""Navigate Spec"",
                    ""precondition"": [
                        ""User has logged in""
                    ],
                    ""steps"": [
                        {
                            ""action"": ""Navigate to page1"",
                            ""expected"": """"
                        },
                        {
                            ""action"": ""Navigate to users"",
                            ""expected"": """"
                        }
                    ],
                    ""finalExpected"": ""User has successfully navigated to the page""
                },
                {
                    ""title"": ""Navigate Spec"",
                    ""precondition"": [
                        ""User has logged in""
                    ],
                    ""steps"": [
                        {
                            ""action"": ""Navigate to page1"",
                            ""expected"": """"
                        },
                        {
                            ""action"": ""Navigate to users"",
                            ""expected"": """"
                        }
                    ],
                    ""finalExpected"": ""User has successfully navigated to the page""
                }
            ]
        }
    }
]"