from datetime import datetime
from braintrust import Eval
import braintrust

# Sample data for evaluation
def get_eval_data():
    return [
        {
            "input": "What's the weather like today?",
            "expected": "I'd need to know your location and check current weather data to give you an accurate forecast for today.",
            "metadata": {"category": "weather_query"}
        },
        {
            "input": "Is the stock market open today?",
            "expected": "The stock market is typically open Monday through Friday, but I'd need to check if today is a trading day and account for holidays.",
            "metadata": {"category": "market_query"}
        },
        {
            "input": "What day of the week is it?",
            "expected": f"Today is {datetime.now().strftime('%A')}.",
            "metadata": {"category": "date_query"}
        }
    ]

# Task function that simulates an LLM response
def task_function(input_text, hooks):
    # Add current date to metadata for reference
    hooks.metadata["evaluation_date"] = datetime.now().strftime("%Y-%m-%d")
    hooks.metadata["evaluation_time"] = datetime.now().strftime("%H:%M:%S")
    
    # Simulate different LLM responses based on input
    if "weather" in input_text.lower():
        return "I can't check current weather conditions, but you can check a weather app for today's forecast."
    elif "stock market" in input_text.lower():
        return "The stock market is open today during regular trading hours (9:30 AM - 4:00 PM ET)."
    elif "day of the week" in input_text.lower():
        return f"Today is {datetime.now().strftime('%A')}."
    else:
        return "I'm not sure about that specific question."

# Method 1: Dynamic LLM Scorer Creation (Recommended)
def create_date_aware_scorer():
    current_date = datetime.now().strftime("%Y-%m-%d")
    current_day = datetime.now().strftime("%A")
    
    project = braintrust.projects.create(name="date-aware-scorers")
    
    return project.scorers.create(
        name=f"Date Context Scorer {current_date}",
        slug=f"date-context-scorer-{current_date.replace('-', '')}",
        description=f"Evaluates responses considering today's date: {current_date}",
        messages=[
            {
                "role": "system",
                "content": f"""You are an expert evaluator. Today's date is {current_date} ({current_day}).
Your job is to evaluate whether responses appropriately consider today's date and time context."""
            },
            {
                "role": "user",
                "content": """Please evaluate this response considering today's date context:

Input Question: {{input}}
AI Response: {{output}}
Expected Response: {{expected}}

Does the AI response appropriately handle today's date context?

A) Excellent - Response perfectly considers today's date/time
B) Good - Response somewhat considers date context  
C) Poor - Response ignores or incorrectly handles date context

Return only the letter of your choice."""
            }
        ],
        model="gpt-4o",
        use_cot=True,
        choice_scores={"A": 1.0, "B": 0.7, "C": 0.0},
    )

# Method 2: Code-based scorer with dynamic date injection
def date_aware_code_scorer(input_text: str, output: str, expected: str, metadata: dict) -> dict:
    """Custom scorer that evaluates date awareness in responses"""
    from openai import OpenAI
    
    current_date = datetime.now().strftime("%Y-%m-%d")
    current_day = datetime.now().strftime("%A")
    
    # Create the prompt with current date
    prompt = f"""You are evaluating AI responses for date awareness.

Today's date: {current_date} ({current_day})

Input: {input_text}
Output: {output}
Expected: {expected}

Rate the output's date awareness:
- Score 1.0: Perfect date awareness
- Score 0.7: Good date awareness  
- Score 0.3: Poor date awareness
- Score 0.0: No date awareness

Return only a number between 0.0 and 1.0."""

    client = OpenAI()  # Make sure you have OPENAI_API_KEY set
    
    try:
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[{"role": "user", "content": prompt}],
            temperature=0,
            max_tokens=10
        )
        
        score_text = response.choices[0].message.content.strip()
        score = float(score_text)
        
        return {
            "score": max(0.0, min(1.0, score)),  # Clamp between 0 and 1
            "name": "date_awareness",
            "metadata": {
                "evaluation_date": current_date,
                "evaluation_day": current_day,
                "llm_response": score_text,
                "category": metadata.get("category", "unknown")
            }
        }
    except Exception as e:
        return {
            "score": 0.0,
            "name": "date_awareness", 
            "metadata": {
                "error": str(e),
                "evaluation_date": current_date
            }
        }

# Method 3: Using autoevals with dynamic template
from autoevals import LLMClassifier

def create_date_classifier():
    current_date = datetime.now().strftime("%Y-%m-%d")
    current_day = datetime.now().strftime("%A")
    
    return LLMClassifier(
        name="Date Context Classifier",
        prompt_template=f"""Today is {current_date} ({current_day}).

Evaluate this AI response for date/time awareness:

Input: {{{{input}}}}
Output: {{{{output}}}}

Does the response show appropriate awareness of today's date?

A) Yes - Shows good date/time awareness
B) Partial - Shows some date awareness but could be better
C) No - Shows poor or no date awareness""",
        choice_scores={"A": 1.0, "B": 0.5, "C": 0.0},
        use_cot=True,
    )

# Run the evaluation with all three scoring methods
if __name__ == "__main__":
    # Create scorers
    llm_scorer = create_date_aware_scorer()
    classifier_scorer = create_date_classifier()
    
    # Run evaluation
    result = Eval(
        "Pedro-Date Aware Bot",
        data=get_eval_data,
        task=task_function,
        scores=[
            llm_scorer,                    # Method 1: Dynamic LLM scorer
            date_aware_code_scorer,        # Method 2: Code-based scorer  
            classifier_scorer,             # Method 3: autoevals classifier
        ],
        metadata={
            "evaluation_date": datetime.now().strftime("%Y-%m-%d"),
            "evaluation_timestamp": datetime.now().isoformat(),
            "purpose": "Test date awareness in AI responses"
        }
    )
    
    print("Evaluation completed!")
    print(f"Summary: {result.summary}")