#!/usr/bin/env python3
"""
Example script demonstrating how to use the BraintrustClient class.

This script shows various ways to create project automations using the BraintrustClient,
including webhook automations, BTQL export automations, and retention policies.
"""

import os
import json
from typing import Dict, Any
import requests


class BraintrustClient:
    def __init__(self, api_url: str, org_name: str, api_key: str):
        self.api_url = api_url.rstrip('/')
        self.org_name = org_name
        self.headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }

    def _request(self, method: str, endpoint: str, **kwargs) -> Dict:
        url = f"{self.api_url}{endpoint}"
        params = kwargs.pop('params', {})
        params['org_name'] = self.org_name

        response = requests.request(method, url, headers=self.headers, params=params, **kwargs)
        response.raise_for_status()
        return response.json()

    def create_project_automation(self, project_id: str, name: str, config: Dict) -> Dict:
        """Create a new project automation."""
        data = {
            'project_id': project_id,
            'name': name,
            'config': config
        }
        return self._request('POST', '/v1/project_automation', json=data)


def create_webhook_automation_config(
    btql_filter: str,
    webhook_url: str,
    interval_seconds: int = 3600
) -> Dict[str, Any]:
    """
    Create a webhook automation configuration.
    
    Args:
        btql_filter: BTQL filter to identify rows for the automation rule
        webhook_url: The webhook URL to send the request to
        interval_seconds: Perform the action at most once in this interval (1-2592000 seconds)
    
    Returns:
        Configuration dictionary for webhook automation
    """
    return {
        "event_type": "logs",
        "btql_filter": btql_filter,
        "interval_seconds": interval_seconds,
        "action": {
            "type": "webhook",
            "url": webhook_url
        }
    }


def create_btql_export_automation_config(
    export_path: str,
    format: str = "jsonl",
    interval_seconds: int = 86400,
    batch_size: int = 1000,
    export_type: str = "log_traces",
    btql_query: str = None,
    aws_access_key_id: str = None,
    aws_secret_access_key: str = None
) -> Dict[str, Any]:
    """
    Create a BTQL export automation configuration.
    
    Args:
        export_path: Path to export results (e.g., "s3://bucket-name/path/to/export")
        format: Export format ("jsonl" or "parquet")
        interval_seconds: Export interval in seconds
        batch_size: Number of rows to export in each batch
        export_type: Type of export ("log_traces", "log_spans", or "btql_query")
        btql_query: BTQL query (required if export_type is "btql_query")
        aws_access_key_id: AWS access key for S3 exports
        aws_secret_access_key: AWS secret key for S3 exports
    
    Returns:
        Configuration dictionary for BTQL export automation
    """
    export_definition = {"type": export_type}
    if export_type == "btql_query" and btql_query:
        export_definition["query"] = btql_query
    
    config = {
        "event_type": "btql_export",
        "export_definition": export_definition,
        "export_path": export_path,
        "format": format,
        "interval_seconds": interval_seconds,
        "batch_size": batch_size,
        "credentials": {}
    }
    
    # Add AWS credentials if provided
    if aws_access_key_id and aws_secret_access_key:
        config["credentials"] = {
            "aws_access_key_id": aws_access_key_id,
            "aws_secret_access_key": aws_secret_access_key
        }
    
    return config


def create_retention_automation_config(
    object_type: str,
    retention_days: int
) -> Dict[str, Any]:
    """
    Create a retention automation configuration.
    
    Args:
        object_type: Object type ("project_logs", "experiment", or "dataset")
        retention_days: Number of days to retain the object
    
    Returns:
        Configuration dictionary for retention automation
    """
    return {
        "event_type": "retention",
        "object_type": object_type,
        "retention_days": retention_days
    }


def main():
    """Main function demonstrating BraintrustClient usage."""
    
    # Configuration - replace with your actual values
    API_URL = os.getenv("BRAINTRUST_API_URL", "https://api.braintrust.dev")
    ORG_NAME = os.getenv("BRAINTRUST_ORG_NAME", "your-org-name")
    API_KEY = os.getenv("BRAINTRUST_API_KEY", "your-api-key")
    PROJECT_ID = os.getenv("BRAINTRUST_PROJECT_ID", "your-project-id")
    
    # Validate required environment variables
    if API_KEY == "your-api-key":
        print("⚠️  Please set BRAINTRUST_API_KEY environment variable")
        print("   export BRAINTRUST_API_KEY='your-actual-api-key'")
        return
    
    if ORG_NAME == "your-org-name":
        print("⚠️  Please set BRAINTRUST_ORG_NAME environment variable")
        print("   export BRAINTRUST_ORG_NAME='your-actual-org-name'")
        return
    
    if PROJECT_ID == "your-project-id":
        print("⚠️  Please set BRAINTRUST_PROJECT_ID environment variable")
        print("   export BRAINTRUST_PROJECT_ID='your-actual-project-id'")
        return
    
    # Initialize the client
    client = BraintrustClient(
        api_url=API_URL,
        org_name=ORG_NAME,
        api_key=API_KEY
    )
    
    print(f"🚀 Initialized BraintrustClient for org: {ORG_NAME}")
    print(f"   API URL: {API_URL}")
    print(f"   Project ID: {PROJECT_ID}")
    print()
    
    # Example 1: Create a webhook automation
    print("📡 Creating webhook automation...")
    try:
        webhook_config = create_webhook_automation_config(
            btql_filter="error_count > 0",
            webhook_url="https://your-webhook-endpoint.com/alerts",
            interval_seconds=1800  # 30 minutes
        )
        
        webhook_automation = client.create_project_automation(
            project_id=PROJECT_ID,
            name="Error Alert Webhook",
            config=webhook_config
        )
        
        print(f"✅ Created webhook automation: {webhook_automation.get('name')}")
        print(f"   ID: {webhook_automation.get('id')}")
        print(f"   Config: {json.dumps(webhook_config, indent=2)}")
        print()
        
    except Exception as e:
        print(f"❌ Failed to create webhook automation: {e}")
        print()
    
    # Example 2: Create a BTQL export automation
    print("📊 Creating BTQL export automation...")
    try:
        export_config = create_btql_export_automation_config(
            export_path="s3://my-bucket/braintrust-exports/",
            format="parquet",
            interval_seconds=86400,  # Daily
            batch_size=5000,
            export_type="log_traces",
            aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
            aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY")
        )
        
        export_automation = client.create_project_automation(
            project_id=PROJECT_ID,
            name="Daily Log Export",
            config=export_config
        )
        
        print(f"✅ Created export automation: {export_automation.get('name')}")
        print(f"   ID: {export_automation.get('id')}")
        print(f"   Export path: {export_config['export_path']}")
        print()
        
    except Exception as e:
        print(f"❌ Failed to create export automation: {e}")
        print()
    
    # Example 3: Create a retention policy automation
    print("🗂️  Creating retention policy automation...")
    try:
        retention_config = create_retention_automation_config(
            object_type="project_logs",
            retention_days=90
        )
        
        retention_automation = client.create_project_automation(
            project_id=PROJECT_ID,
            name="90-Day Log Retention",
            config=retention_config
        )
        
        print(f"✅ Created retention automation: {retention_automation.get('name')}")
        print(f"   ID: {retention_automation.get('id')}")
        print(f"   Retention: {retention_config['retention_days']} days")
        print()
        
    except Exception as e:
        print(f"❌ Failed to create retention automation: {e}")
        print()
    
    print("🎉 Example completed!")
    print("\n💡 Tips:")
    print("   - Set environment variables for your actual API credentials")
    print("   - Modify the BTQL filters and configurations as needed")
    print("   - Check the Braintrust dashboard to see your created automations")


if __name__ == "__main__":
    main()
