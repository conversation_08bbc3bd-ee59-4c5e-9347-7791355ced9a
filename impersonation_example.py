import os
 
import requests
 
# If you're self-hosting Braintrust, then use your stack's Universal API URL, e.g.
# https://dfwhllz61x709.cloudfront.net
BRAINTRUST_API_URL = "https://api.braintrust.dev"
# API_KEY = os.environ["PEDRO_ORG_BRAINTRUST_API_KEY"]
API_KEY = os.environ["PEDRO_ORG_BRAINTRUST_API_KEY"]
 
 
def get_owner_role_id():
    resp = requests.get(
        f"{BRAINTRUST_API_URL}/v1/role",
        headers={"Authorization": f"Bearer {API_KEY}"},
        params=dict(role_name="Owner"),
    )
    resp.raise_for_status()
    return resp.json()["objects"][0]["id"]
 
 
def get_user_org_info(org_name):
    resp = requests.post(
        f"{BRAINTRUST_API_URL}/api/self/me",
        headers={"Authorization": f"Bearer {API_KEY}"},
    )
    resp.raise_for_status()
    me_info = resp.json()
    org_info = [x for x in me_info["organizations"] if x["name"] == org_name]
    # for i in me_info["organizations"]:
    #     if i['name'] == org_name:
    #         org_info = i
    if not org_info:
        raise Exception(f"No organization found with name {org_name}")
    return dict(user_id=me_info["id"], org_id=org_info["id"])
 
 
def grant_ownership_role(org_name):
    owner_role_id = get_owner_role_id()
    # owner_role_id = 'd077a4a3-03c3-4ac7-a1e2-2cad91464b03'
    user_org_info = get_user_org_info(org_name)
 
    # Grant an 'Owner' ACL to the requesting user on the organization. Granting
    # this ACL requires the user to have `create_acls` permission on the org,
    # which means they must already be an owner of the org indirectly.
    resp = requests.post(
        f"{BRAINTRUST_API_URL}/v1/acl",
        headers={"Authorization": f"Bearer {API_KEY}"},
        json=dict(
            object_type="organization",
            object_id=user_org_info["org_id"],
            user_id=user_org_info["user_id"],
            role_id=owner_role_id,
        ),
    )
    resp.raise_for_status()
 
 
def main():
    # This only needs to be done once.
    grant_ownership_role('pedro-test')
 
    # This will only succeed if the user being impersonated has permissions to
    # create a project within the org.
    resp = requests.post(
        f"{BRAINTRUST_API_URL}/v1/project",
        headers={
            "Authorization": f"Bearer {API_KEY}",
            "x-bt-impersonate-user": '<EMAIL>',
        },
        json=dict(
            name="impersonation-made1",
            org_name='pedro-test',
        ),
    )
    resp.raise_for_status()
    print(resp.json())

main()