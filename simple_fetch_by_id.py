#!/usr/bin/env python3
"""
Simple example of fetching an experiment by ID and then using braintrust.init().

This approach:
1. Uses the experiment ID to get experiment metadata (including name and project)
2. Then uses the standard braintrust.init() with the retrieved name and project

Usage:
    python simple_fetch_by_id.py

Make sure to:
1. Install braintrust: pip install braintrust
2. Set your BRAINTRUST_API_KEY environment variable
3. Update EXPERIMENT_ID below
"""

import braintrust
from braintrust.logger import _state

# Configuration - update with your actual experiment ID
EXPERIMENT_ID = "7bf54658-4eec-4537-b08d-b47d97620fc3"

def get_experiment_info_by_id(experiment_id: str):
    """
    Get experiment name and project info by experiment ID.
    
    Returns:
        dict: Contains experiment_name, project_name, project_id, and full experiment info
    """
    
    # Ensure we're logged in
    braintrust.login()
    
    # Get experiment info by ID
    exp_response = _state.app_conn().post_json("api/experiment/get", {
        "id": experiment_id
    })
    
    if len(exp_response) == 0:
        raise ValueError(f"Experiment with ID {experiment_id} not found.")
    
    experiment_info = exp_response[0]
    
    # Get project info
    project_response = _state.app_conn().post_json("api/project/get", {
        "id": experiment_info['project_id']
    })
    
    if len(project_response) == 0:
        raise ValueError(f"Project with ID {experiment_info['project_id']} not found.")
    
    project_info = project_response[0]
    
    return {
        'experiment_name': experiment_info['name'],
        'project_name': project_info['name'],
        'project_id': experiment_info['project_id'],
        'experiment_info': experiment_info,
        'project_info': project_info
    }

def main():
    try:
        print(f"Looking up experiment ID: {EXPERIMENT_ID}")
        
        # Get experiment and project names from the ID
        info = get_experiment_info_by_id(EXPERIMENT_ID)
        
        print(f"✓ Found experiment: '{info['experiment_name']}'")
        print(f"✓ In project: '{info['project_name']}'")
        print(f"✓ Created: {info['experiment_info'].get('created', 'N/A')}")
        
        # Now use the standard braintrust.init() approach
        print(f"\nFetching experiment using braintrust.init()...")
        
        experiment = braintrust.init(
            project=info['project_name'],
            experiment=info['experiment_name'],
            open=True  # Read-only mode
        )
        
        print(f"✓ Experiment loaded successfully!")
        print(f"Experiment ID: {experiment.id}")
        print(f"ID matches: {experiment.id == EXPERIMENT_ID}")
        
        # Show experiment records
        print("\nExperiment records:")
        print("-" * 40)
        
        record_count = 0
        for i, record in enumerate(experiment.fetch()):
            record_count += 1
            print(f"Record {record_count}:")
            print(f"  Input: {record.get('input')}")
            print(f"  Output: {record.get('output')}")
            print(f"  Expected: {record.get('expected')}")
            print(f"  Scores: {record.get('scores')}")
            print()
            
            # Limit for demo
            if i >= 2:
                print("... (showing first 3 records)")
                break
        
        if record_count == 0:
            print("No records found in the experiment.")
        
        # Show dataset conversion
        print(f"\nConverting to dataset format...")
        dataset_records = list(experiment.as_dataset())
        print(f"Dataset records available: {len(dataset_records)}")
        
    except ValueError as e:
        print(f"❌ Error: {e}")
        print("\nTroubleshooting:")
        print("- Check that the experiment ID is correct and exists")
        print("- Verify you have access to the experiment and project")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        print("- Make sure BRAINTRUST_API_KEY is set")
        print("- Check your internet connection")

def extract_id_from_url(url: str) -> str:
    """
    Helper to extract experiment ID from Braintrust URL.
    
    Example: https://www.braintrust.dev/app/org/p/project/experiments/exp-id
    Returns: exp-id
    """
    parts = url.rstrip('/').split('/')
    if 'experiments' in parts:
        idx = parts.index('experiments')
        if idx + 1 < len(parts):
            return parts[idx + 1]
    raise ValueError("Could not extract experiment ID from URL")

if __name__ == "__main__":
    # Uncomment and modify this if you have an experiment URL instead of ID:
    # experiment_url = "https://www.braintrust.dev/app/your-org/p/project/experiments/your-exp-id"
    # EXPERIMENT_ID = extract_id_from_url(experiment_url)
    # print(f"Extracted ID: {EXPERIMENT_ID}")
    
    main()
