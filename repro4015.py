"""
Evaluation script for Test Plan Generation Agent
"""
# Standard library imports
import os
import sys
import json
import re
from typing import Optional, List
import pandas as pd

# Third-party imports
import braintrust
from braintrust import Eva<PERSON>
from autoevals import LLMClassifier
from openai import OpenAI

# Local imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from shared.utils import load_csv_data, load_config

# Constants
PROJECT_NAME = "Test Plan Agent Evaluation"
MODEL_NAME = "gpt-4o"

# Create OpenAI client with cache disabled headers for Braintrust
braintrust_client = OpenAI(
    base_url="https://api.braintrust.dev/v1/proxy",
    api_key=os.getenv("BRAINTRUST_API_KEY"),
    default_headers={
        "x-bt-use-cache": "never"
    }
)

def create_testplan_scorer():
    """Create a custom LLM-as-a-judge scorer for test plan evaluation"""
    
    def testplan_llm_judge(args):
        """Custom scorer using LLMClassifier from autoevals"""
        
        # Extract required fields
        output = args.get('output', '')
        prd_document = args.get('prd_document', '')
        technical_document = args.get('technical_document', '')
        test_format = args.get('test_format', '')
        
        # Create the evaluation prompt using LLMClassifier template system
        prompt = """# Enhanced Functional UI Test Plan Evaluator

You are an expert Senior Software QA Architect with deep experience in functional UI testing, test planning, product requirements analysis, and technical documentation review. Your role is to assess the quality and completeness of UI test plans with a focus on validating both **positive (happy path)** and **negative (edge/error) test flows**, **and** on the overall **test plan document structure** derived from the product and technical specifications.

---
## Input Files You Will Receive
You will receive four inputs from the user:
1. **[product]-testplan.json**: The test plan to be validated - {{output}}
2. **[product]-prd.yaml**: Product Requirements Document (source of truth) - {{prd_document}}
3. **[product]-td.json**: Technical Documentation (source of truth) - {{technical_document}}
4. **[testformat].json**: Required test-plan format specification - {{test_format}}

> **IMPORTANT:** If any of these inputs is missing, malformed, or not valid JSON/YAML format, immediately assign Grade **F**, return the JSON output (see below) with `"status":"Fail"`, include specific details about what is missing or malformed, and stop further evaluation.

---
## Pre-Evaluation Validation Checklist

Before beginning the detailed evaluation, verify:

### File Format Validation:
- **Test Plan JSON**: Valid JSON structure with required fields

### Content Completeness Check:
- **Format Spec**: Defines test case template, required fields, naming conventions

> **CRITICAL**: If any validation fails, assign Grade **F** and provide specific remediation steps.

---
## Enhanced Evaluation Framework

You will evaluate the **test plan** across **four** categories, totaling **100 points**:

---

### 1. Test Plan Document Quality & Structure (15 points)
**Enhanced scoring for comprehensive document assessment:**

- **Scope & Objectives (4 pts)**  
  - Clear, measurable scope statement with explicit in-scope vs out-of-scope boundaries
  - Test objectives directly aligned to PRD goals and business requirements
  - Success criteria clearly defined

- **Assumptions, Dependencies & Risks (3 pts)**  
  - Comprehensive list of assumptions with validation methods
  - External dependencies identified with mitigation strategies
  - Risk assessment with probability/impact matrix and contingency plans

- **Test Environment & Data Strategy (4 pts)**  
  - Detailed environment specifications (hardware/OS/browser matrix)
  - Test data management strategy (creation, cleanup, privacy compliance)
  - Tool specifications with version numbers and configuration details

- **Entry & Exit Criteria (3 pts)**  
  - Quantifiable entry criteria (code coverage, build stability, environment readiness)
  - Measurable exit criteria (pass/fail thresholds, defect closure criteria)
  - Quality gates and approval workflows defined

- **Traceability & Reporting Strategy (1 pt)**  
  - Requirements traceability matrix approach
  - Test metrics and reporting framework
  - Defect management and escalation procedures

---

### 2. Positive Test Flows (50 points)
**Refined scoring with enhanced coverage requirements:**

#### Core Coverage Requirements (25 pts):

- **Complete PRD Feature Coverage (8 pts)**
  - Every user story from PRD has corresponding test cases
  - All acceptance criteria are testable and covered
  - Feature interactions and dependencies tested
  - Cross-reference validation: PRD features → Test cases (1:1 minimum mapping)

- **Technical Specification Alignment (7 pts)**
  - All technical constraints from TD are validated in tests
  - Performance requirements translated to functional test assertions
  - Integration points and data flow scenarios covered
  - Technical edge cases identified and tested

- **UI Component Testing (5 pts)**
  - **Atomic level**: Every atom (buttons, inputs, icons) individually tested
  - **Molecular level**: Component combinations and interactions tested
  - **Organism level**: Complex UI sections and their behaviors tested
  - Visual and functional validation for each component level

- **User Journey Completeness (5 pts)**
  - End-to-end user flows for each persona from PRD
  - Multi-page navigation scenarios with state preservation
  - User workflow variations and alternative paths
  - Cross-functional feature integration testing

#### Test Case Quality & Detail (15 pts):

- **Test Case Structure & Clarity (5 pts)**
  - Exact adherence to [testformat].json specification
  - Consistent naming conventions and organization
  - Clear preconditions, steps, and expected results
  - Actionable for junior QA team members

- **Granularity & Precision (5 pts)**
  - Atomic test steps that can't be further subdivided
  - Specific UI element identification (IDs, classes, xpath when needed)
  - Detailed expected outcomes for each interaction
  - Clear pass/fail criteria for each assertion

- **Test Data & State Management (5 pts)**
  - Comprehensive test data scenarios (valid, boundary, realistic)
  - Initial state setup and cleanup procedures
  - Data dependencies and isolation strategies
  - Mock data requirements and generation methods

#### Advanced Coverage Areas (10 pts):

- **Multi-Filter & Search Combinations (3 pts)**
  - All filter combinations tested systematically
  - Search functionality with various input types
  - Filter + search combination scenarios
  - Result sorting and pagination testing

- **State Management & Navigation (4 pts)**
  - UI state persistence across page navigation
  - Browser back/forward button behavior
  - Page refresh and session management
  - Deep linking and URL state management

- **Cross-Browser & Responsive Testing (3 pts)**
  - Test cases specify browser/device variations
  - Responsive design breakpoint testing
  - Touch vs mouse interaction scenarios
  - Mobile-specific functionality validation

---

### 3. Negative Test Flows (30 points)
**Enhanced negative testing with comprehensive edge case coverage:**

#### Input Validation & Boundary Testing (10 pts):
- **Invalid Input Scenarios (4 pts)**
  - All input fields tested with invalid data types
  - Boundary value testing (min/max lengths, ranges)
  - Special character and injection attempt testing
  - Empty, null, and undefined input handling

- **Form Validation Completeness (3 pts)**
  - Client-side validation testing for all forms
  - Required field validation across all forms
  - Format validation (email, phone, dates, etc.)
  - Real-time validation feedback testing

- **File Upload & Media Handling (3 pts)**
  - Invalid file types and oversized files
  - Corrupted file upload scenarios
  - Missing file and empty upload testing
  - Security validation for file uploads

#### Error Handling & User Experience (10 pts):
- **Error Message Quality (4 pts)**
  - User-friendly error messages matching PRD language
  - Contextual help and guidance provided
  - Consistent error styling and placement
  - Accessibility compliance for error states

- **System Resilience Testing (3 pts)**
  - Network interruption and timeout scenarios
  - Concurrent user action conflicts
  - Session expiration and authentication failures
  - Graceful degradation under system stress

- **Recovery & Retry Mechanisms (3 pts)**
  - User ability to recover from error states
  - Retry functionality for failed operations
  - Data preservation during error recovery
  - Clear recovery path instructions

#### Security & Access Control (5 pts):
- **Authorization Testing (3 pts)**
  - Unauthorized access attempt scenarios
  - Role-based access control validation
  - Privilege escalation prevention testing
  - Session hijacking and timeout testing

- **Data Protection & Privacy (2 pts)**
  - Sensitive data masking and protection
  - GDPR/privacy compliance scenarios
  - Data export/deletion functionality
  - Audit trail and logging validation

---

### 4. Test Plan Execution Readiness (5 points)

- **Team Execution Capability (5 pts)**
  - Test cases appropriate for team skill level
  - Clear role assignments and responsibilities
  - Knowledge transfer and training requirements

---

## Final Report Format - Make sure that the output is in the below format:

- **Grade**: A (90–100), B (80–89), C (70–79), D (60–69), F (<60)  
- **Score Breakdown**:  
  - Test-Plan Quality: X/15  
  - Positive Test Flows: Y/50  
  - Negative Test Flows: Z/30  
  - Execution Readiness: W/5

- **Feedback**:  
  - **Strengths**  
  - **Gaps/Risks**  
  - **Improvement Suggestions**  

---
## Quality Assurance Guidelines

1. **Be Objective**: Score based on measurable criteria, not subjective preferences
2. **Be Thorough**: Check every requirement and cross-reference systematically
3. **Be Practical**: Consider real-world execution challenges and team capabilities
4. **Be Constructive**: Provide actionable feedback that leads to improvement
5. **Be Consistent**: Apply the same standards across all test plan evaluations

> **Remember**: 
- Your evaluation directly impacts product quality and team success. Be rigorous but fair, detailed but concise, critical but constructive.
- You only evaluate **functional UI test plans**, not backend or API test cases.
- If the documents provided are incomplete or not in expected format, you must return Grade **'F'** immediately.
- You must focus on **functional correctness and user-facing flows**, not performance or accessibility. """
        
        # Use LLMClassifier with properly cleaned data to avoid JSON parsing issues
        try:
            # Clean and prepare data for LLMClassifier
            def clean_data_for_classifier(text, max_length=3000):
                if not isinstance(text, str):
                    return str(text)
                
                # Normalize line endings first
                text = text.replace('\r\n', '\n').replace('\r', '\n')
                
                # Remove control characters that cause JSON parsing issues
                # Keep only printable ASCII characters plus newlines and tabs
                text = ''.join(char for char in text if ord(char) >= 32 or char in '\n\t')
                
                # Fix common JSON escaping issues that cause template problems
                text = text.replace('\\', '\\\\')  # Escape backslashes
                text = text.replace('"', '\\"')   # Escape quotes to prevent template issues
                text = text.replace('{', '\\{')   # Escape braces to prevent template conflicts
                text = text.replace('}', '\\}')   # Escape braces to prevent template conflicts
                
                # Truncate to manageable size
                if len(text) > max_length:
                    text = text[:max_length]
                    # Try to end at a reasonable boundary
                    if not text.endswith(('\\n', ' ', '\\t')):
                        last_space = text.rfind(' ')
                        if last_space > max_length * 0.9:
                            text = text[:last_space]
                
                return text
            
            # Clean the data
            output_clean = clean_data_for_classifier(output, 4000)
            prd_clean = clean_data_for_classifier(prd_document, 2000)
            td_clean = clean_data_for_classifier(technical_document, 2000)
            format_clean = clean_data_for_classifier(test_format, 1000)
            
            # Use LLMClassifier from autoevals with chain-of-thought for detailed rationale
            classifier = LLMClassifier(
                name="TestPlan LLM Judge",
                prompt_template=prompt,
                choice_scores={
                    "A": 0.9,  # 90-100% score for excellent
                    "B": 0.8,  # 80-89% score for good  
                    "C": 0.7,  # 70-79% score for adequate
                    "D": 0.6,  # 60-69% score for below average
                    "F": 0.5   # <60% score for poor
                },
                model=MODEL_NAME,
                client=braintrust_client,  # Use Braintrust proxy client
                use_cot=True  # Enable chain-of-thought for detailed reasoning
            )
            
            # Call the classifier with cleaned data
            result = classifier(
                output=output_clean,
                expected="",  # Not used in this evaluation
                prd_document=prd_clean,
                technical_document=td_clean,
                test_format=format_clean
            )
            
        except Exception as e:
            print(f"LLMClassifier error: {e}")
            # Return a fallback score
            from autoevals import Score
            result = Score(
                name="TestPlan LLM Judge",
                score=0.0,
                metadata={
                    "error": f"LLMClassifier failed: {str(e)}",
                    "grade": "F"
                }
            )
        
        return result
    
    return testplan_llm_judge

# Create the scorer instance
testplan_llm_scorer = create_testplan_scorer()

# Helper functions to extract information from scorer metadata
def extract_rationale(score_result):
    """
    Extract the detailed rationale/reasoning from a scorer result.
    
    Args:
        score_result: The result from a scorer (Score object or dict)
        
    Returns:
        str: The detailed rationale/reasoning, or a default message if not found
    """
    if hasattr(score_result, 'metadata') and score_result.metadata:
        # Try different possible keys for rationale
        rationale_keys = ['rationale', 'reasoning', 'explanation', 'analysis']
        for key in rationale_keys:
            if key in score_result.metadata and score_result.metadata[key]:
                return score_result.metadata[key]
    
    # Fallback if no rationale found
    return "No detailed rationale available"

def extract_grade(score_result):
    """
    Extract the letter grade from a scorer result.
    
    Args:
        score_result: The result from a scorer (Score object or dict)
        
    Returns:
        str: The letter grade (A, B, C, D, F), or 'Unknown' if not found
    """
    if hasattr(score_result, 'metadata') and score_result.metadata:
        if 'grade' in score_result.metadata:
            return score_result.metadata['grade']
    
    # Try to infer grade from score if no explicit grade
    if hasattr(score_result, 'score'):
        score = score_result.score
        if score >= 0.9:
            return 'A'
        elif score >= 0.8:
            return 'B'
        elif score >= 0.7:
            return 'C'
        elif score >= 0.6:
            return 'D'
        else:
            return 'F'
    
    return 'Unknown'

def extract_score_breakdown(score_result):
    """
    Extract detailed score breakdown from a scorer result.
    
    Args:
        score_result: The result from a scorer (Score object or dict)
        
    Returns:
        dict: Dictionary with score breakdown, or empty dict if not found
    """
    if hasattr(score_result, 'metadata') and score_result.metadata:
        breakdown_keys = ['score_breakdown', 'breakdown', 'scores', 'details']
        for key in breakdown_keys:
            if key in score_result.metadata and score_result.metadata[key]:
                return score_result.metadata[key]
    
    return {}

def format_evaluation_summary(score_result):
    """
    Format a comprehensive evaluation summary from a scorer result.
    
    Args:
        score_result: The result from a scorer (Score object or dict)
        
    Returns:
        str: Formatted summary of the evaluation
    """
    grade = extract_grade(score_result)
    score = getattr(score_result, 'score', 'Unknown')
    rationale = extract_rationale(score_result)
    breakdown = extract_score_breakdown(score_result)
    
    score_display = f"{score:.2f}" if isinstance(score, (int, float)) else str(score)
    
    summary = f"""
=== Test Plan Evaluation Summary ===
Grade: {grade}
Score: {score_display}

Detailed Analysis:
{rationale}
"""
    
    if breakdown:
        summary += f"\nScore Breakdown:\n"
        for category, points in breakdown.items():
            summary += f"  - {category}: {points}\n"
    
    return summary

def print_evaluation_results(score_results):
    """
    Print formatted evaluation results for multiple test cases.
    
    Args:
        score_results: List of scorer results or single scorer result
    """
    if not isinstance(score_results, list):
        score_results = [score_results]
    
    print("\n" + "="*60)
    print("TEST PLAN EVALUATION RESULTS")
    print("="*60)
    
    for i, result in enumerate(score_results, 1):
        print(f"\n--- Test Case {i} ---")
        print(format_evaluation_summary(result))
    
    # Overall statistics
    if len(score_results) > 1:
        scores = [getattr(r, 'score', 0) for r in score_results if hasattr(r, 'score')]
        grades = [extract_grade(r) for r in score_results]
        
        if scores:
            avg_score = sum(scores) / len(scores)
            print(f"\n--- Overall Statistics ---")
            print(f"Average Score: {avg_score:.2f}")
            grade_counts = {g: grades.count(g) for g in set(grades)}
            print(f"Grade Distribution: {grade_counts}")

# Example usage function for testing the helpers
def test_helper_functions():
    """Test the helper functions with sample data"""
    from autoevals import Score
    
    # Create a sample score result
    sample_result = Score(
        name="TestPlan LLM Judge",
        score=0.8,
        metadata={
            "rationale": "The test plan demonstrates good coverage of positive test flows and adequate negative testing. However, it lacks comprehensive documentation structure and could benefit from more detailed execution readiness criteria.",
            "grade": "B",
            "score_breakdown": {
                "Test Plan Quality": "12/15",
                "Positive Test Flows": "40/50", 
                "Negative Test Flows": "24/30",
                "Execution Readiness": "4/5"
            }
        }
    )
    
    # Test the helper functions
    print("Testing helper functions:")
    print(f"Grade: {extract_grade(sample_result)}")
    print(f"Rationale: {extract_rationale(sample_result)[:100]}...")
    print(f"Breakdown: {extract_score_breakdown(sample_result)}")
    print(format_evaluation_summary(sample_result))



def testplan_agent_task(input_data):
    """
    Task function: Returns the test plan output that needs to be evaluated.
    This follows Braintrust's architecture where task generates output, scorer evaluates it.
    """
    # Extract from the input structure (Braintrust format)
    if isinstance(input_data, dict) and "input" in input_data:
        actual_input = input_data["input"]
    else:
        actual_input = input_data
    
    # Extract the agent output (test plan) that needs to be evaluated
    if isinstance(actual_input, dict):
        agent_output = actual_input.get("agent_output", "")
    else:
        agent_output = str(actual_input)
    
    # Return the test plan output for evaluation by the scorer
    return agent_output



class DataLoader:
    """Loads evaluation data from CSV or XLSX files with robust error handling."""
    
    def __init__(self, file_path: str, encoding: str = 'utf-8'):
        self.file_path = file_path
        self.encoding = encoding
        self.file_extension = os.path.splitext(file_path)[1].lower()
    
    def _read_file(self) -> 'pd.DataFrame':
        """
        Read the input file based on its extension.
        
        Returns:
            pd.DataFrame: The loaded data
        
        Raises:
            ValueError: If file extension is not supported
            Exception: If there's an error reading the file
        """
        try:
            if self.file_extension == '.csv':
                return pd.read_csv(self.file_path, encoding=self.encoding)
            elif self.file_extension in ['.xlsx', '.xls']:
                # For Excel files, we don't need encoding parameter
                return pd.read_excel(self.file_path)
            else:
                raise ValueError(f"Unsupported file extension: {self.file_extension}. Supported formats: .csv, .xlsx, .xls")
        except Exception as e:
            print(f"Error reading file {self.file_path}: {str(e)}")
            raise
    
    def pandas_json_validator(self, df: 'pd.DataFrame', columns_to_validate: List[str]) -> 'pd.DataFrame':
        """
        Validates JSON in specified columns of a pandas DataFrame.
        
        Args:
            df: pandas DataFrame containing the data
            columns_to_validate: List of column names that should contain valid JSON
            
        Returns:
            DataFrame with new columns indicating JSON validity for each specified column
        """
        def is_valid_json(text) -> bool:
            if pd.isna(text):
                return False
            try:
                if not isinstance(text, str):
                    text = str(text)
                json.loads(text)
                return True
            except (json.JSONDecodeError, TypeError):
                return False
        
        # Create validation columns for each specified column
        for col in columns_to_validate:
            if col in df.columns:
                validation_col = f"{col}_is_valid_json"
                df[validation_col] = df[col].apply(is_valid_json)
                
                # Print summary of invalid JSON entries
                invalid_count = (~df[validation_col]).sum()
                if invalid_count > 0:
                    print(f"Found {invalid_count} invalid JSON entries in column '{col}'")
                    # Get indices of invalid entries
                    invalid_indices = df[~df[validation_col]].index.tolist()
                    print(f"Invalid JSON found at rows (0-based index): {invalid_indices}")
        
        return df
    
    def load_data(self, limit: Optional[int] = None) -> List[dict]:
        """Load evaluation data from CSV/XLSX file with proper error handling."""
        try:
            df = self._read_file()
            
            if limit:
                df = df.head(limit)
            
            print(f"Loading {len(df)} rows from {self.file_path}")
            
            # Validate JSON in relevant columns
            json_columns = ['agent_output','prd_document','technical_document','test_format']
            df = self.pandas_json_validator(df, json_columns)
            
            evaluation_inputs = []
            for idx, row in df.iterrows():
                try:
                    # Handle metadata column if it exists
                    metadata = {"row_index": idx}
                    if 'metadata' in df.columns and pd.notna(row['metadata']):
                        if isinstance(row['metadata'], str):
                            try:
                                parsed_metadata = json.loads(row['metadata'])
                                metadata.update(parsed_metadata)
                            except json.JSONDecodeError:
                                print(f"Warning: Invalid JSON in metadata for row {idx}")
                                metadata['raw_metadata'] = str(row['metadata'])
                    
                    # Map CSV column names to expected field names
                    # Handle both old and new column naming conventions
                    prd_value = row.get('prd_document', row.get('prd', ''))
                    tech_spec_value = row.get('technical_document', row.get('tech_spec', ''))
                    test_format_value = row.get('test_format', row.get('test_plan_format', ''))
                    generated_testplan_value = row.get('agent_output', row.get('generated_testplan', ''))
                    
                    # Clean the data to remove problematic characters
                    def clean_text_data(text):
                        if not isinstance(text, str):
                            return str(text)
                        # Remove control characters except newlines and tabs
                        cleaned = ''.join(char for char in text if ord(char) >= 32 or char in '\n\t\r')
                        # Handle CSV escaping
                        if '""' in cleaned:
                            cleaned = cleaned.replace('""', '"')
                        return cleaned
                    
                    # Create the input structure expected by Braintrust
                    eval_input = {
                        "input": {
                            "agent_output": clean_text_data(generated_testplan_value),
                            "prd_document": clean_text_data(prd_value),
                            "technical_document": clean_text_data(tech_spec_value),
                            "test_format": clean_text_data(test_format_value)
                        },
                        "metadata": metadata
                    }
                    evaluation_inputs.append(eval_input)
                    
                except Exception as e:
                    print(f"Error processing row {idx}: {e}")
                    # Continue with other rows
                    continue
            
            print(f"Successfully loaded {len(evaluation_inputs)} evaluation inputs")
            return evaluation_inputs
            
        except Exception as e:
            print(f"Error loading CSV data: {e}")
            # Return fallback data
            return [
                {
                    "input": {
                        "agent_output": "Sample test plan JSON",
                        "prd_document": "Sample PRD document",
                        "technical_document": "Sample technical document", 
                        "test_format": "Sample test format specification"
                    },
                    "metadata": {
                        "complexity": "medium",
                        "source": "fallback_data"
                    }
                }
            ]

def load_testplan_data():
    """Load Test Plan evaluation dataset from CSV/XLSX using robust loader"""
    data_path = os.path.join(os.path.dirname(__file__), 'data', 'testplan_eval_dataset_micro.xlsx')
    loader = DataLoader(data_path)
    return loader.load_data()

def clean_csv_escaped_json(data):
    """
    Simple function to clean CSV-escaped JSON data.
    Note: Most cleaning is now handled by CSVDataLoader.
    """
    if not isinstance(data, str) or not data.strip():
        return data
    
    # Basic cleaning - the CSVDataLoader should have handled most issues
    cleaned = data.strip()
    
    # Handle any remaining CSV escaping
    if '""' in cleaned:
        cleaned = cleaned.replace('""', '"')
    
    return cleaned

# Create a simple wrapper for the scorer to handle the data format
def testplan_scorer(output, expected=None, **kwargs):
    """Simple scorer wrapper that handles data extraction and calls our custom LLM judge"""
    try:
        # Extract input data
        input_data = kwargs.get('input', {})
        
        # Get the documents from input data
        prd_document = input_data.get('prd_document', '')
        test_format = input_data.get('test_format', '')
        technical_document = input_data.get('technical_document', '')
        
        # Clean CSV-escaped JSON data with robust handling
        output = clean_csv_escaped_json(output)
        prd_document = clean_csv_escaped_json(prd_document)
        test_format = clean_csv_escaped_json(test_format)
        technical_document = clean_csv_escaped_json(technical_document)
        
        # Validate that we have the required data
        if not output or not prd_document or not technical_document or not test_format:
            print(f"Warning: Missing required data - output: {bool(output)}, prd: {bool(prd_document)}, td: {bool(technical_document)}, format: {bool(test_format)}")
        
        # Call our custom LLM judge scorer
        return testplan_llm_scorer({
            'output': output,
            'expected': expected,
            'prd_document': prd_document,
            'technical_document': technical_document,
            'test_format': test_format
        })
        
    except Exception as e:
        print(f"Error in testplan_scorer: {e}")
        # Return a default failing score in case of error
        from autoevals import Score
        return Score(
            name="TestPlan LLM Judge",
            score=0.0,
            metadata={
                "error": str(e),
                "grade": "F"
            }
        )

# Define the evaluation at module level for braintrust push
Eval(
    name=PROJECT_NAME,
    data=load_testplan_data,
    task=lambda input_data: testplan_agent_task(input_data),
    scores=[
        testplan_scorer
    ],
    metadata={
        "agent_type": "TestPlan",
        "version": "4.0.0",
        "description": f"Evaluates Test Plan agent using LLMClassifierFromTemplate with {MODEL_NAME} model"
    }
) 