#!/usr/bin/env python3
"""
Example script showing how to run an experiment using the Braintrust SDK.

This script demonstrates:
1. How to initialize an experiment
2. How to log data to the experiment (inputs, outputs, scores, metadata)
3. How to get experiment summaries and results
4. Different ways to structure experiment data

Prerequisites:
- Install braintrust: pip install braintrust
- Set BRAINTRUST_API_KEY environment variable or provide it as a parameter
"""

import braintrust
import time
import random
from typing import List, Dict, Any


def simple_model_function(input_text: str) -> str:
    """
    Example model function - replace this with your actual model/API call.
    
    This simulates a simple text processing model that adds a prefix.
    In practice, this would be your LLM call, API request, or model inference.
    """
    # Simulate some processing time
    time.sleep(0.1)
    
    # Simple transformation - in practice this would be your model
    return f"Processed: {input_text.upper()}"


def calculate_accuracy_score(input_text: str, output: str, expected: str) -> float:
    """
    Example scoring function that calculates accuracy.
    
    Returns 1.0 if output matches expected, 0.0 otherwise.
    """
    return 1.0 if output == expected else 0.0


def calculate_length_score(input_text: str, output: str, expected: str) -> float:
    """
    Example scoring function that compares output length to expected length.
    
    Returns a score based on how close the output length is to expected length.
    """
    if not expected or not output:
        return 0.0
    
    expected_len = len(expected)
    output_len = len(output)
    
    if expected_len == 0:
        return 1.0 if output_len == 0 else 0.0
    
    # Score based on length similarity (closer to 1.0 is better)
    ratio = min(expected_len, output_len) / max(expected_len, output_len)
    return ratio


def run_basic_experiment():
    """
    Run a basic experiment with manual data logging.
    """
    print("Running basic experiment...")
    
    # Initialize the experiment
    experiment = braintrust.init(
        project="my-experiment-project",
        experiment="basic-text-processing-experiment",
        description="Testing a simple text processing model"
    )
    
    # Test data - in practice this might come from a dataset or file
    test_cases = [
        {"input": "hello world", "expected": "Processed: HELLO WORLD"},
        {"input": "python programming", "expected": "Processed: PYTHON PROGRAMMING"},
        {"input": "machine learning", "expected": "Processed: MACHINE LEARNING"},
        {"input": "artificial intelligence", "expected": "Processed: ARTIFICIAL INTELLIGENCE"},
        {"input": "data science", "expected": "Processed: DATA SCIENCE"},
    ]
    
    print(f"Running {len(test_cases)} test cases...")
    
    # Process each test case and log to experiment
    for i, test_case in enumerate(test_cases):
        input_text = test_case["input"]
        expected_output = test_case["expected"]
        
        # Run your model/function
        actual_output = simple_model_function(input_text)
        
        # Calculate scores
        accuracy = calculate_accuracy_score(input_text, actual_output, expected_output)
        length_score = calculate_length_score(input_text, actual_output, expected_output)
        
        # Log the result to the experiment
        experiment.log(
            input=input_text,
            output=actual_output,
            expected=expected_output,
            scores={
                "accuracy": accuracy,
                "length_similarity": length_score,
            },
            metadata={
                "test_case_id": i,
                "input_length": len(input_text),
                "output_length": len(actual_output),
                "processing_time": 0.1,  # In practice, measure actual time
            },
            tags=["text-processing", "basic-test"]
        )
        
        print(f"  Test case {i+1}: '{input_text}' -> '{actual_output}' (accuracy: {accuracy})")
    
    # Get experiment summary
    print("\nExperiment completed! Getting summary...")
    summary = experiment.summarize()
    
    print(f"\nExperiment Summary:")
    print(f"  Project: {summary.project_name}")
    print(f"  Experiment: {summary.experiment_name}")
    print(f"  URL: {summary.experiment_url}")
    
    if summary.scores:
        print(f"  Scores:")
        for score in summary.scores:
            print(f"    {score.name}: {score.score:.3f}")
    
    return experiment, summary


def run_experiment_with_dataset():
    """
    Run an experiment using an existing dataset.
    """
    print("\nRunning experiment with dataset...")
    
    # You can also associate an experiment with a dataset
    try:
        # First, try to get an existing dataset
        dataset = braintrust.init_dataset(
            project="my-experiment-project",
            name="text-processing-dataset"
        )
        print(f"Using existing dataset: {dataset.name}")
    except Exception as e:
        print(f"Dataset not found, creating sample data instead: {e}")
        dataset = None
    
    # Initialize experiment with dataset
    experiment = braintrust.init(
        project="my-experiment-project",
        experiment="dataset-based-experiment",
        description="Testing with dataset integration",
        dataset=dataset  # Associate with dataset if available
    )
    
    # Sample data (in practice this would come from your dataset)
    sample_data = [
        {"input": "test input 1", "expected": "Processed: TEST INPUT 1"},
        {"input": "test input 2", "expected": "Processed: TEST INPUT 2"},
    ]
    
    for item in sample_data:
        output = simple_model_function(item["input"])
        accuracy = calculate_accuracy_score(item["input"], output, item["expected"])
        
        experiment.log(
            input=item["input"],
            output=output,
            expected=item["expected"],
            scores={"accuracy": accuracy},
            metadata={"source": "dataset" if dataset else "sample"}
        )
    
    summary = experiment.summarize()
    print(f"Dataset experiment completed: {summary.experiment_url}")
    
    return experiment, summary


def run_experiment_with_error_handling():
    """
    Run an experiment with proper error handling and logging.
    """
    print("\nRunning experiment with error handling...")
    
    experiment = braintrust.init(
        project="my-experiment-project",
        experiment="error-handling-experiment",
        description="Testing error handling in experiments"
    )
    
    test_cases = [
        {"input": "normal input", "should_fail": False},
        {"input": "error input", "should_fail": True},
        {"input": "another normal input", "should_fail": False},
    ]
    
    for i, test_case in enumerate(test_cases):
        input_text = test_case["input"]
        
        try:
            if test_case["should_fail"]:
                # Simulate an error
                raise ValueError(f"Simulated error for input: {input_text}")
            
            output = simple_model_function(input_text)
            
            experiment.log(
                input=input_text,
                output=output,
                scores={"success": 1.0},
                metadata={"test_case": i, "status": "success"}
            )
            
        except Exception as e:
            # Log the error to the experiment
            experiment.log(
                input=input_text,
                output=None,
                error=str(e),
                scores={"success": 0.0},
                metadata={"test_case": i, "status": "error", "error_type": type(e).__name__}
            )
            print(f"  Error in test case {i}: {e}")
    
    summary = experiment.summarize()
    print(f"Error handling experiment completed: {summary.experiment_url}")
    
    return experiment, summary


def main():
    """
    Main function demonstrating different ways to run experiments.
    """
    print("Braintrust Experiment Examples")
    print("=" * 50)
    
    try:
        # Run basic experiment
        exp1, summary1 = run_basic_experiment()
        
        # Run experiment with dataset
        exp2, summary2 = run_experiment_with_dataset()
        
        # Run experiment with error handling
        exp3, summary3 = run_experiment_with_error_handling()
        
        print("\n" + "=" * 50)
        print("All experiments completed successfully!")
        print("\nExperiment URLs:")
        print(f"1. Basic experiment: {summary1.experiment_url}")
        print(f"2. Dataset experiment: {summary2.experiment_url}")
        print(f"3. Error handling experiment: {summary3.experiment_url}")
        
        # Flush all experiments to ensure data is sent
        print("\nFlushing experiments...")
        exp1.flush()
        exp2.flush()
        exp3.flush()
        print("Done!")
        
    except Exception as e:
        print(f"Error running experiments: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
