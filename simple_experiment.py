#!/usr/bin/env python3
"""
Simple example of running an experiment using Braintrust SDK.

Usage:
    python simple_experiment.py

Make sure to:
1. Install braintrust: pip install braintrust
2. Set your BRAINTRUST_API_KEY environment variable
"""

import braintrust

def my_model(input_text):
    """Simple example model - replace with your actual model/function."""
    return f"Response to: {input_text}"

def accuracy_score(input_val, output, expected):
    """Simple scoring function."""
    return 1.0 if output == expected else 0.0

def main():
    # Initialize experiment
    experiment = braintrust.init(
        project="pedro-project1",
        experiment="simple-test",
        description="A simple experiment example"
    )
    
    # Test cases
    test_cases = [
        {"input": "Hello", "expected": "Response to: Hello"},
        {"input": "World", "expected": "Response to: World"},
        {"input": "Test", "expected": "Response to: Test"},
    ]
    
    # Run experiment
    print("Running experiment...")
    for i, case in enumerate(test_cases):
        # Get model output
        output = my_model(case["input"])
        
        # Calculate score
        score = accuracy_score(case["input"], output, case["expected"])
        
        # Log to experiment
        experiment.log(
            input=case["input"],
            output=output,
            expected=case["expected"],
            scores={"accuracy": score},
            metadata={"test_id": i}
        )
        
        print(f"Test {i+1}: {case['input']} -> {output} (score: {score})")
    
    # Get summary
    summary = experiment.summarize()
    print(f"\nExperiment completed!")
    print(f"URL: {summary.experiment_url}")
    print(f"Average accuracy: {summary.scores[0].score if summary.scores else 'N/A'}")

if __name__ == "__main__":
    main()
