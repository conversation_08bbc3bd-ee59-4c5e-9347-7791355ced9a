#!/usr/bin/env python3
"""
Example script showing how to fetch an existing experiment using the Braintrust SDK.

This script demonstrates:
1. How to authenticate with Braintrust
2. How to fetch an existing experiment by project and experiment name using open=True
3. How to iterate through experiment records and events
4. How to access experiment metadata and properties
5. How to convert experiment data to dataset format for reuse

Prerequisites:
- Install braintrust: pip install braintrust
- Set BRAINTRUST_API_KEY environment variable or provide it as a parameter
- Have an existing experiment in your Braintrust project
"""

import os
import braintrust
from typing import Optional


def fetch_experiment_example(
    project_name: str,
    experiment_name: str,
    api_key: Optional[str] = None,
    org_name: Optional[str] = None,
    app_url: Optional[str] = None
):
    """
    Fetch an existing experiment from Braintrust.
    
    Args:
        project_name: Name of the project containing the experiment
        experiment_name: Name of the experiment to fetch
        api_key: Braintrust API key (optional, will use BRAINTRUST_API_KEY env var if not provided)
        org_name: Organization name (optional)
        app_url: Braintrust app URL (optional, defaults to https://www.braintrust.dev)
    
    Returns:
        ReadonlyExperiment object that can be used to access records and metadata
    """
    
    # Initialize the experiment with open=True to fetch existing experiment
    experiment = braintrust.init(
        project=project_name,
        experiment=experiment_name,
        open=True,  # This is the key parameter to fetch existing experiments
        api_key=api_key,
        org_name=org_name,
        app_url=app_url
    )
    
    return experiment


def explore_experiment(experiment):
    """
    Explore and display information about the experiment.
    
    Args:
        experiment: Braintrust ReadonlyExperiment object
    """
    
    print(f"Experiment ID: {experiment.id}")
    print(f"Experiment Name: {experiment.name}")
    
    # Count records
    record_count = 0
    print("\nExperiment Records:")
    print("-" * 60)
    
    # Iterate through all records in the experiment
    for record in experiment.fetch():
        record_count += 1
        print(f"Record {record_count}:")
        
        # Display common fields that are typically present in experiment records
        if 'input' in record:
            print(f"  Input: {record['input']}")
        if 'output' in record:
            print(f"  Output: {record['output']}")
        if 'expected' in record:
            print(f"  Expected: {record['expected']}")
        if 'scores' in record:
            print(f"  Scores: {record['scores']}")
        if 'metadata' in record:
            print(f"  Metadata: {record['metadata']}")
        if 'tags' in record:
            print(f"  Tags: {record['tags']}")
        if 'created' in record:
            print(f"  Created: {record['created']}")
        if 'span_id' in record:
            print(f"  Span ID: {record['span_id']}")
        
        print()  # Empty line for readability
        
        # Limit output for demo purposes
        if record_count >= 5:
            print(f"... (showing first 5 records out of many)")
            break
    
    print(f"Total records processed: {record_count}")


def experiment_as_dataset_example(experiment):
    """
    Demonstrate how to use an experiment as a dataset for evaluations.
    
    Args:
        experiment: Braintrust ReadonlyExperiment object
    """
    
    print("\n" + "=" * 60)
    print("Converting Experiment to Dataset Format:")
    print("=" * 60)
    
    # Convert experiment to dataset format
    dataset_records = list(experiment.as_dataset())
    
    print(f"Dataset records from experiment: {len(dataset_records)}")
    
    for i, record in enumerate(dataset_records[:3]):  # Show first 3
        print(f"Dataset Record {i+1}:")
        print(f"  Input: {record.get('input')}")
        print(f"  Expected: {record.get('expected')}")
        if 'tags' in record:
            print(f"  Tags: {record.get('tags')}")
        print()
    
    return dataset_records


def main():
    """
    Main function demonstrating how to fetch and explore an experiment.
    """
    
    # Configuration - modify these values for your specific experiment
    PROJECT_NAME = "your-project-name"  # Replace with your project name
    EXPERIMENT_NAME = "your-experiment-name"  # Replace with your experiment name
    
    # Optional: specify API key directly (otherwise uses BRAINTRUST_API_KEY env var)
    API_KEY = os.getenv("BRAINTRUST_API_KEY")
    
    if not API_KEY:
        print("Warning: No API key found. Set BRAINTRUST_API_KEY environment variable.")
        print("You can also pass api_key parameter to braintrust.init().")
        return
    
    try:
        print(f"Fetching experiment '{EXPERIMENT_NAME}' from project '{PROJECT_NAME}'...")
        
        # Fetch the existing experiment
        experiment = fetch_experiment_example(
            project_name=PROJECT_NAME,
            experiment_name=EXPERIMENT_NAME,
            api_key=API_KEY
        )
        
        print("Experiment fetched successfully!")
        print("=" * 60)
        
        # Explore the experiment
        explore_experiment(experiment)
        
        # Show how to use experiment as dataset
        dataset_records = experiment_as_dataset_example(experiment)
        
        print("\n" + "=" * 60)
        print("Example: Using this experiment data in a new evaluation:")
        print("=" * 60)
        print("```python")
        print("from braintrust import Eval")
        print("")
        print("# Fetch your existing experiment")
        print(f"experiment = braintrust.init(project='{PROJECT_NAME}', experiment='{EXPERIMENT_NAME}', open=True)")
        print("")
        print("# Use it as dataset in a new evaluation")
        print("Eval(")
        print("    'New Evaluation Based on Previous Experiment',")
        print("    data=experiment.as_dataset(),  # Use the experiment data as dataset")
        print("    task=lambda input: your_improved_model_function(input),")
        print("    scores=[your_scoring_function]")
        print(")")
        print("```")
        
        print("\nExperiment exploration complete!")
        
    except ValueError as e:
        if "not found" in str(e):
            print(f"Error: {e}")
            print("\nTroubleshooting tips:")
            print("1. Make sure the project name and experiment name are correct")
            print("2. Verify the experiment exists in Braintrust")
            print("3. Check that you have access to the specified project and experiment")
        else:
            print(f"Error: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")
        print("\nTroubleshooting tips:")
        print("1. Verify your API key is valid")
        print("2. Check your internet connection")
        print("3. Ensure you have the latest version of braintrust installed")


if __name__ == "__main__":
    main()
