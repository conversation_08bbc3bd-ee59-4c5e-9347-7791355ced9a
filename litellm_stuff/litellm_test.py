from litellm import completion
import os

## set ENV variables
# os.environ["OPENAI_API_KEY"] = "your-api-key"

# openai call
# response = completion(
#   model="openai/gpt-4o",
#   messages=[{ "content": "Hello, how are you?","role": "user"}]
# )

# anthropic call
response = completion(
  model="anthropic/claude-sonnet-4-5",
  messages=[{ "content": "Hello, how are you?","role": "user"}]
)

print(response)