"""
Script to fetch an existing dataset named "test123" from Braintrust
"""

import braintrust
from braintrust import Eval<PERSON><PERSON>
from typing import List, Dict, Any

def fetch_dataset_test123(project_name: str = "test123") -> None:
    """
    Fetch and display records from the existing dataset "test123"
    
    Args:
        project_name: The project name where the dataset exists
    """
    try:
        print(f"Fetching dataset 'test123' from project '{project_name}'...")
        
        # Initialize connection to existing dataset
        dataset = braintrust.init_dataset(project=project_name, name="test123")
        
        # Get dataset summary
        print("\n=== Dataset Summary ===")
        summary = dataset.summarize()
        print(f"Dataset summary: {summary}")
        
        # Fetch and display all records
        print("\n=== Dataset Records ===")
        records = []
        record_count = 0
        
        for record in dataset:
            record_count += 1
            records.append(record)
            print(f"\nRecord {record_count}:")
            print(f"  ID: {record.get('id', 'N/A')}")
            print(f"  Input: {record.get('input', 'N/A')}")
            print(f"  Expected: {record.get('expected', 'N/A')}")
            print(f"  Output: {record.get('output', 'N/A')}")  # Some datasets use 'output' instead of 'expected'
            print(f"  Metadata: {record.get('metadata', 'N/A')}")
            print(f"  Tags: {record.get('tags', 'N/A')}")
            print(f"  Created: {record.get('created', 'N/A')}")
        
        print(f"\nTotal records found: {record_count}")
        
        return records
        
    except Exception as e:
        print(f"Error fetching dataset: {e}")
        print("\nPossible issues:")
        print("1. Dataset 'test123' doesn't exist")
        print("2. Project name is incorrect")
        print("3. You don't have access to this dataset")
        print("4. API key is not set or invalid")
        return []

def convert_to_evalcases(records: List[Dict[str, Any]]) -> List[EvalCase]:
    """
    Convert dataset records to EvalCase objects for use in evaluations
    
    Args:
        records: List of dataset records
        
    Returns:
        List of EvalCase objects
    """
    eval_cases = []
    
    for record in records:
        # Handle both 'expected' and 'output' fields (datasets can use either)
        expected_value = record.get('expected') or record.get('output')
        
        eval_case = EvalCase(
            input=record.get('input'),
            expected=expected_value,
            metadata=record.get('metadata'),
            tags=record.get('tags')
        )
        eval_cases.append(eval_case)
    
    return eval_cases

def use_dataset_in_evaluation(project_name: str = "test123") -> None:
    """
    Example of how to use the fetched dataset in an evaluation
    """
    from autoevals import Levenshtein
    
    print("\n=== Using Dataset in Evaluation ===")
    
    # Method 1: Use dataset directly in Eval
    print("Method 1: Using dataset directly...")
    try:
        dataset = braintrust.init_dataset(project=project_name, name="test123")
        
        # Simple task function for demonstration
        def simple_task(input_data):
            # This is just a placeholder - replace with your actual task
            if isinstance(input_data, str):
                return f"Processed: {input_data}"
            elif isinstance(input_data, dict):
                return f"Processed: {input_data}"
            else:
                return f"Processed: {str(input_data)}"
        
        # Use the dataset directly in an evaluation
        # Note: Uncomment the lines below to actually run the evaluation
        """
        from braintrust import Eval
        
        results = Eval(
            "Test123 Dataset Evaluation",
            data=dataset,  # Use dataset directly
            task=simple_task,
            scores=[Levenshtein],
            experiment_name="Fetch Test123 Dataset"
        )
        
        print(f"Evaluation completed with {len(results.results)} test cases")
        """
        
        print("Dataset is ready for evaluation (evaluation code commented out)")
        
    except Exception as e:
        print(f"Error using dataset in evaluation: {e}")
    
    # Method 2: Convert to EvalCase objects first
    print("\nMethod 2: Converting to EvalCase objects...")
    try:
        records = fetch_dataset_test123(project_name)
        if records:
            eval_cases = convert_to_evalcases(records)
            print(f"Converted {len(eval_cases)} records to EvalCase objects")
            
            # Show first EvalCase as example
            if eval_cases:
                print(f"\nFirst EvalCase:")
                first_case = eval_cases[0]
                print(f"  Input: {first_case.input}")
                print(f"  Expected: {first_case.expected}")
                print(f"  Metadata: {first_case.metadata}")
                print(f"  Tags: {first_case.tags}")
        
    except Exception as e:
        print(f"Error converting to EvalCase objects: {e}")

def fetch_with_filtering(project_name: str = "test123") -> None:
    """
    Example of fetching dataset records with filtering using fetch() method
    """
    print("\n=== Fetching with Manual Filtering ===")
    
    try:
        dataset = braintrust.init_dataset(project=project_name, name="test123")
        
        # Use fetch() method to get records (allows for more control)
        print("Fetching records using fetch() method...")
        
        records = []
        for record in dataset.fetch():
            records.append(record)
        
        print(f"Fetched {len(records)} records using fetch() method")
        
        # Example: Filter records manually
        if records:
            # Filter records that have tags
            tagged_records = [r for r in records if r.get('tags')]
            print(f"Records with tags: {len(tagged_records)}")
            
            # Filter records by metadata
            records_with_metadata = [r for r in records if r.get('metadata')]
            print(f"Records with metadata: {len(records_with_metadata)}")
        
    except Exception as e:
        print(f"Error fetching with filtering: {e}")

if __name__ == "__main__":
    print("=== Braintrust Dataset Fetcher ===")
    print("Fetching dataset 'test123'...\n")
    
    # You can change the project name if needed
    PROJECT_NAME = "pedro-project1"  # Change this if your dataset is in a different project
    
    # Fetch the dataset
    records = fetch_dataset_test123(PROJECT_NAME)
    
    # Show how to use it in evaluations
    if records:
        use_dataset_in_evaluation(PROJECT_NAME)
        fetch_with_filtering(PROJECT_NAME)
    else:
        print("\nNo records found. Please check:")
        print("1. Dataset name is correct ('test123')")
        print("2. Project name is correct")
        print("3. You have access to the dataset")
        print("4. Your BRAINTRUST_API_KEY is set correctly")
