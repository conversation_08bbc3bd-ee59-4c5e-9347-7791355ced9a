"""
Shared assets for Strands Agent demo workflows.

This module contains common setup code used across multiple demo examples:
- Hook implementations for error handling and metrics
- LLM model initialization helpers
- OpenTelemetry and Braintrust logging configuration

Purpose: Reduce code duplication across demo examples while keeping
         agent-specific logic in individual demo files.
"""

import logging
import os

from opentelemetry import metrics, trace
from opentelemetry.exporter.otlp.proto.http.trace_exporter import OTLPSpanExporter
from opentelemetry.sdk.resources import Resource
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor, ConsoleSpanExporter
from strands.experimental.hooks.events import AfterModelInvocationEvent
from strands.hooks import HookProvider, HookRegistry
from strands.hooks.events import MessageAddedEvent
from strands.models.litellm import LiteLLMModel
from strands.telemetry import StrandsTelemetry

# Set up logging
logger = logging.getLogger(__name__)

# Set up OpenTelemetry metrics
meter = metrics.get_meter(__name__)
model_invocation_error = meter.create_counter("maestro_model_invocation_error", unit="1")


# ============================================================================
# HOOK IMPLEMENTATION
# ============================================================================


class FailFastOnToolError(HookProvider):
    """
    A hook that fails the agent immediately if any tool returns an error status.
    This is to prevent the agent from continuing when any tool has failed.
    """

    def register_hooks(self, registry: HookRegistry) -> None:
        registry.add_callback(AfterModelInvocationEvent, self._on_after_model_invocation_event)
        registry.add_callback(MessageAddedEvent, self._on_message)

    def _on_message(self, event: MessageAddedEvent) -> None:
        # MessageAddedEvent fires whenever the agent appends a message to history.
        # We watch for toolResult blocks and fail on the first error.
        for block in event.message.get("content", []):
            tr = block.get("toolResult")
            if tr and tr.get("status") == "error":
                # Surface any text payload from the tool as the exception message
                details = next((c.get("text") for c in tr.get("content", []) if "text" in c), "")
                raise RuntimeError(f"Tool failure: {details or 'no details'}")

    def _on_after_model_invocation_event(self, event: AfterModelInvocationEvent) -> None:
        """
        This hook is called after every model invocation, whether successful or
        failed. Add a metric tracking exception types.
        """
        if e := event.exception:
            model_invocation_error.add(
                1, attributes={"exception_type": f"{type(e).__module__}.{type(e).__name__}"}
            )
            trace.get_current_span().record_exception(e)


# ============================================================================
# LLM INITIALIZATION HELPER
# ============================================================================


def setup_llm_model(model_id: str) -> LiteLLMModel:
    """
    Initialize and return a LiteLLM model.

    For Airbnb developers: Uses airbnb_litellm.initialize()
    For external users: Comment out the initialize() call and ensure your API keys are in .env

    Args:
        model_id: The model ID to use

    Returns:
        LiteLLMModel: Configured model instance
    """

    # Comment out the next 2 lines if you're not using Airbnb-specific setup
    from airbnb_litellm import initialize

    initialize(model_id)

    return LiteLLMModel(model_id=model_id)


# ============================================================================
# OPENTELEMETRY AND BRAINTRUST INITIALIZATION
# BRAINTRUST_PROJECT_ID, BRAINTRUST_API_KEY, OTEL_EXPORTER_OTLP_ENDPOINT should
# be included in environment variables
# ============================================================================


def opentelemetry_init(console: bool = True):
    """
    Set up logging with OpenTelemetry and Braintrust.

    This function configures:
    - OpenTelemetry tracer provider with OTLP exporter
    - Optional console exporter for local debugging
    - Strands telemetry integration

    Args:
        console: If True, also export traces to console for debugging

    Environment variables required:
        BRAINTRUST_PROJECT_ID: Your Braintrust project ID
        BRAINTRUST_API_KEY: Your Braintrust API key
        OTEL_EXPORTER_OTLP_ENDPOINT: OTLP endpoint URL (default: https://api.braintrust.dev/otel/v1/traces)
    """
    braintrust_project_id = os.getenv("BRAINTRUST_PROJECT_ID")
    braintrust_api_key = os.getenv("BRAINTRUST_API_KEY")

    bt_parent = f"project_id:{braintrust_project_id}"
    headers = {
        "authorization": f"bearer {braintrust_api_key}",
        "x-bt-parent": bt_parent,
    }

    resource = Resource(attributes={})
    tracer_provider = TracerProvider(resource=resource)
    processor = BatchSpanProcessor(
        OTLPSpanExporter(endpoint=os.getenv("OTEL_EXPORTER_OTLP_ENDPOINT"), headers=headers)
    )
    tracer_provider.add_span_processor(processor)

    if console:
        console_processor = BatchSpanProcessor(ConsoleSpanExporter())
        tracer_provider.add_span_processor(console_processor)

    trace.set_tracer_provider(tracer_provider)

    # Set up strands telemetry
    strands_telemetry = StrandsTelemetry()
    strands_telemetry.setup_otlp_exporter(
        endpoint=os.getenv("OTEL_EXPORTER_OTLP_ENDPOINT"), headers=headers
    )

    if console:
        strands_telemetry.setup_console_exporter()
