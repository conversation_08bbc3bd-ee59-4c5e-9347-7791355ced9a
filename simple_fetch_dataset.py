#!/usr/bin/env python3
"""
Simple example of fetching an existing dataset using Braintrust SDK.

Usage:
    python simple_fetch_dataset.py

Make sure to:
1. Install braintrust: pip install braintrust
2. Set your BRAINTRUST_API_KEY environment variable
3. Update PROJECT_NAME and DATASET_NAME below
"""

import braintrust

# Configuration - update these with your actual project and dataset names
PROJECT_NAME = "pedro-project1"
DATASET_NAME = "themes"

def main():
    # Fetch existing dataset
    dataset = braintrust.init_dataset(
        project=PROJECT_NAME,
        name=DATASET_NAME
    )
    
    # Print basic dataset info
    print(f"Dataset: {dataset.name}")
    print(f"Project: {dataset.project.name}")
    print(f"Dataset ID: {dataset.id}")
    
    # Iterate through records
    print("\nDataset records:")
    for i, record in enumerate(dataset.fetch()):
        print(f"Record {i+1}: {record}")
        
        # Stop after first 3 records for demo
        if i >= 2:
            break
    
    print(f"\nDataset fetched successfully!")

if __name__ == "__main__":
    main()
