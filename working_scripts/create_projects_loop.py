#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create multiple Braintrust projects in a loop with incrementing names.

This script converts the curl command:
curl --location 'https://api.braintrust.dev/v1/project' \
--header 'Authorization: Bearer sk-MKotJ5zAzgPSKUYkGbQ1WYgmPRm0c4tO543xLTD0VCj6hPWH' \
--header 'Content-Type: application/json' \
--data '{"name": "test1"}'

Into a Python loop that creates projects named test1, test2, test3, etc.

Usage:
    python create_projects_loop.py
    
You can modify the script to:
- Change the number of projects to create
- Modify the base name pattern
- Add delays between requests
- Handle errors and retries
"""

import requests
import json
import time
import sys
from typing import Optional


class BraintrustProjectCreator:
    def __init__(self, api_key: str, base_url: str = "https://api.braintrust.dev/v1"):
        """
        Initialize the Braintrust project creator.
        
        Args:
            api_key: Your Braintrust API key
            base_url: Base URL for the Braintrust API
        """
        self.api_key = api_key
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
    
    def create_project(self, project_name: str) -> Optional[dict]:
        """
        Create a single project with the given name.
        
        Args:
            project_name: Name of the project to create
            
        Returns:
            Response data if successful, None if failed
        """
        url = f"{self.base_url}/project"
        data = {"name": project_name}
        
        try:
            print(f"Creating project: {project_name}")
            response = requests.post(
                url,
                headers=self.headers,
                data=json.dumps(data)
            )
            
            if response.status_code == 200 or response.status_code == 201:
                result = response.json()
                print(f"✅ Successfully created project: {project_name}")
                print(f"   Project ID: {result.get('id', 'N/A')}")
                return result
            else:
                print(f"❌ Failed to create project: {project_name}")
                print(f"   Status code: {response.status_code}")
                print(f"   Response: {response.text}")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Error creating project {project_name}: {str(e)}")
            return None
    
    def create_projects_loop(
        self, 
        base_name: str = "test", 
        count: int = 5, 
        start_number: int = 1,
        delay_seconds: float = 1.0
    ) -> list:
        """
        Create multiple projects in a loop with incrementing names.
        
        Args:
            base_name: Base name for projects (e.g., "test" creates "test1", "test2", etc.)
            count: Number of projects to create
            start_number: Starting number for the sequence
            delay_seconds: Delay between requests to avoid rate limiting
            
        Returns:
            List of successfully created project data
        """
        created_projects = []
        
        print(f"Starting to create {count} projects with base name '{base_name}'")
        print(f"Projects will be named: {base_name}{start_number} to {base_name}{start_number + count - 1}")
        print(f"Delay between requests: {delay_seconds} seconds")
        print("-" * 50)
        
        for i in range(count):
            project_number = start_number + i
            project_name = f"{base_name}{project_number}"
            
            result = self.create_project(project_name)
            if result:
                created_projects.append(result)
            
            # Add delay between requests to avoid rate limiting
            if i < count - 1:  # Don't delay after the last request
                print(f"Waiting {delay_seconds} seconds before next request...")
                time.sleep(delay_seconds)
        
        print("-" * 50)
        print(f"Summary: Successfully created {len(created_projects)} out of {count} projects")
        
        return created_projects


def main():
    # Your API key from the curl command
    API_KEY = "sk-MKotJ5zAzgPSKUYkGbQ1WYgmPRm0c4tO543xLTD0VCj6hPWH"
    
    # Configuration - modify these as needed
    BASE_NAME = "test"          # Base name for projects
    PROJECT_COUNT = 5           # Number of projects to create
    START_NUMBER = 1            # Starting number (test1, test2, etc.)
    DELAY_SECONDS = 1.0         # Delay between requests
    
    # Create the project creator
    creator = BraintrustProjectCreator(API_KEY)
    
    try:
        # Create projects in a loop
        created_projects = creator.create_projects_loop(
            base_name=BASE_NAME,
            count=PROJECT_COUNT,
            start_number=START_NUMBER,
            delay_seconds=DELAY_SECONDS
        )
        
        # Print final summary
        if created_projects:
            print("\n🎉 Created projects:")
            for project in created_projects:
                print(f"   - {project.get('name', 'Unknown')} (ID: {project.get('id', 'N/A')})")
        else:
            print("\n😞 No projects were created successfully")
            
    except KeyboardInterrupt:
        print("\n\n⚠️  Script interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
