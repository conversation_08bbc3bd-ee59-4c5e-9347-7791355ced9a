#!/usr/bin/env python3
"""
Braintrust Dataset Fetching Script

This script demonstrates how to fetch existing datasets from Braintrust using the SDK.
It provides multiple methods for retrieving dataset information and data.
"""

import os
import json
from typing import Optional, Dict, Any, List
import braintrust


def setup_braintrust_client(api_key: Optional[str] = None) -> None:
    """
    Set up Braintrust client with API key.
    
    Args:
        api_key: Optional API key. If not provided, will use environment variable.
    """
    if api_key:
        os.environ["BRAINTRUST_API_KEY"] = api_key
    elif not os.environ.get("BRAINTRUST_API_KEY"):
        raise ValueError(
            "Braintrust API key not found. Please provide it as a parameter "
            "or set the BRAINTRUST_API_KEY environment variable."
        )


def fetch_dataset_by_id(dataset_id: str, project_name: Optional[str] = None) -> Optional[Dict[str, Any]]:
    """
    Fetch a dataset by its ID using the Braintrust SDK.
    
    Args:
        dataset_id: The UUID of the dataset to fetch
        project_name: Optional project name for context
        
    Returns:
        Dataset information as a dictionary, or None if not found
    """
    try:
        # Get dataset using the SDK
        dataset = braintrust.datasets.get(id=dataset_id)
        
        if dataset:
            print(f"✅ Successfully fetched dataset: {dataset.name}")
            print(f"   ID: {dataset.id}")
            print(f"   Project: {dataset.project_name}")
            print(f"   Description: {dataset.description or 'No description'}")
            print(f"   Created: {dataset.created}")
            
            return {
                "id": dataset.id,
                "name": dataset.name,
                "project_name": dataset.project_name,
                "description": dataset.description,
                "created": dataset.created,
                "metadata": dataset.metadata
            }
        else:
            print(f"❌ Dataset with ID {dataset_id} not found")
            return None
            
    except Exception as e:
        print(f"❌ Error fetching dataset: {str(e)}")
        return None


def fetch_dataset_by_name(dataset_name: str, project_name: str) -> Optional[Dict[str, Any]]:
    """
    Fetch a dataset by name within a specific project.
    
    Args:
        dataset_name: Name of the dataset
        project_name: Name of the project containing the dataset
        
    Returns:
        Dataset information as a dictionary, or None if not found
    """
    try:
        # List datasets in the project and find by name
        datasets = braintrust.datasets.list(project_name=project_name)
        
        for dataset in datasets:
            if dataset.name == dataset_name:
                print(f"✅ Found dataset '{dataset_name}' in project '{project_name}'")
                print(f"   ID: {dataset.id}")
                print(f"   Description: {dataset.description or 'No description'}")
                print(f"   Created: {dataset.created}")
                
                return {
                    "id": dataset.id,
                    "name": dataset.name,
                    "project_name": dataset.project_name,
                    "description": dataset.description,
                    "created": dataset.created,
                    "metadata": dataset.metadata
                }
        
        print(f"❌ Dataset '{dataset_name}' not found in project '{project_name}'")
        return None
        
    except Exception as e:
        print(f"❌ Error fetching dataset: {str(e)}")
        return None


def fetch_dataset_records(dataset_id: str, limit: Optional[int] = None) -> List[Dict[str, Any]]:
    """
    Fetch records from a dataset.
    
    Args:
        dataset_id: The UUID of the dataset
        limit: Optional limit on number of records to fetch
        
    Returns:
        List of dataset records
    """
    try:
        # Get the dataset first
        dataset = braintrust.datasets.get(id=dataset_id)
        if not dataset:
            print(f"❌ Dataset with ID {dataset_id} not found")
            return []
        
        print(f"📊 Fetching records from dataset: {dataset.name}")
        
        # Fetch records
        records = []
        record_count = 0
        
        for record in dataset.fetch():
            if limit and record_count >= limit:
                break
                
            records.append({
                "id": record.id,
                "input": record.input,
                "expected": record.expected,
                "metadata": record.metadata
            })
            record_count += 1
        
        print(f"✅ Fetched {len(records)} records from dataset")
        return records
        
    except Exception as e:
        print(f"❌ Error fetching dataset records: {str(e)}")
        return []


def list_datasets_in_project(project_name: str) -> List[Dict[str, Any]]:
    """
    List all datasets in a specific project.
    
    Args:
        project_name: Name of the project
        
    Returns:
        List of dataset information dictionaries
    """
    try:
        datasets = braintrust.datasets.list(project_name=project_name)
        dataset_list = []
        
        print(f"📋 Datasets in project '{project_name}':")
        print("-" * 50)
        
        for dataset in datasets:
            dataset_info = {
                "id": dataset.id,
                "name": dataset.name,
                "description": dataset.description,
                "created": dataset.created
            }
            dataset_list.append(dataset_info)
            
            print(f"• {dataset.name}")
            print(f"  ID: {dataset.id}")
            print(f"  Description: {dataset.description or 'No description'}")
            print(f"  Created: {dataset.created}")
            print()
        
        return dataset_list
        
    except Exception as e:
        print(f"❌ Error listing datasets: {str(e)}")
        return []


def main():
    """
    Main function demonstrating dataset fetching capabilities.
    """
    # Configuration - Update these values for your use case
    API_KEY = "sk-in91pI0YSJ2ydhPAVvuJ16U33MlcE6gl0HxT5DI5tk2wsIoU"  # Your API key
    PROJECT_NAME = "pedro-multimodal-mind2web-eval"  # Your project name
    DATASET_ID = "f9be2853-fba3-4ee7-8f62-0d371098e816"  # Your dataset ID
    
    try:
        # Setup Braintrust client
        setup_braintrust_client(API_KEY)
        
        print("🚀 Braintrust Dataset Fetching Demo")
        print("=" * 50)
        
        # Method 1: Fetch dataset by ID
        print("\n1️⃣ Fetching dataset by ID...")
        dataset_info = fetch_dataset_by_id(DATASET_ID)
        
        if dataset_info:
            print(f"\nDataset Info:")
            print(json.dumps(dataset_info, indent=2, default=str))
        
        # Method 2: List all datasets in project
        print(f"\n2️⃣ Listing all datasets in project '{PROJECT_NAME}'...")
        datasets = list_datasets_in_project(PROJECT_NAME)
        
        # Method 3: Fetch dataset records (limited to first 5 for demo)
        print(f"\n3️⃣ Fetching sample records from dataset...")
        records = fetch_dataset_records(DATASET_ID, limit=5)
        
        if records:
            print(f"\nSample Records (showing first {len(records)}):")
            for i, record in enumerate(records, 1):
                print(f"\nRecord {i}:")
                print(f"  ID: {record['id']}")
                print(f"  Input: {str(record['input'])[:100]}...")  # Truncate for display
                print(f"  Expected: {str(record['expected'])[:100]}...")  # Truncate for display
        
        print("\n✅ Demo completed successfully!")
        
    except Exception as e:
        print(f"❌ Demo failed: {str(e)}")


if __name__ == "__main__":
    main()
