import json
def clean_data_for_classifier(text, max_length=3000):
    if not isinstance(text, str):
        return str(text)
    
    # Normalize line endings first
    text = text.replace('\r\n', '\n').replace('\r', '\n')
    
    # Remove control characters that cause JSON parsing issues
    # Keep only printable ASCII characters plus newlines and tabs
    text = ''.join(char for char in text if ord(char) >= 32 or char in '\n\t')
    
    # Fix common JSON escaping issues that cause template problems
    text = text.replace('\\', '\\\\')  # Escape backslashes
    text = text.replace('"', '\\"')   # Escape quotes to prevent template issues
    text = text.replace('{', '\\{')   # Escape braces to prevent template conflicts
    text = text.replace('}', '\\}')   # Escape braces to prevent template conflicts
    
    # Truncate to manageable size
    if len(text) > max_length:
        text = text[:max_length]
        # Try to end at a reasonable boundary
        if not text.endswith(('\\n', ' ', '\\t')):
            last_space = text.rfind(' ')
            if last_space > max_length * 0.9:
                text = text[:last_space]
    
    return text

text = '[{\
    "id": 1,\
    "name": "<PERSON>",\
    "email": "<EMAIL>",\
    "is_active": true,\
    "roles": ["admin", "user"]\
  }]'
x = json.loads(text)
print(x)
blah = clean_data_for_classifier(text)
json.loads(blah)
print(blah)