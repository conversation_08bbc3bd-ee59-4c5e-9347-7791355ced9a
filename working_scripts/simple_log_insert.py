#!/usr/bin/env python3
"""
Simple script to insert a log into a Braintrust project using the SDK.

This is a minimal example showing how to:
1. Initialize a logger for a project
2. Insert a single log entry
3. Flush the log to ensure it's sent to the server

Usage:
    python simple_log_insert.py

Make sure to set your BRAINTRUST_API_KEY environment variable.
"""

import braintrust


def main():
    # Initialize logger for a project (creates project if it doesn't exist)
    logger = braintrust.init_logger(project="pedro-repro4667")
    
    # Insert a log entry
    log_id = logger.log(
        input="What is the weather like today?",
        output="I don't have access to real-time weather data.",
        scores={"helpfulness": 0.3, "accuracy": 1.0},
        metadata={
            "model": "example-model",
            "timestamp": "2024-01-15T10:30:00Z",
            "user_id": "user123"
        },
        tags=["weather", "information_request"]
    )
    
    print(f"Successfully logged entry with ID: {log_id}")
    
    # Flush to ensure the log is sent to the server
    logger.flush()
    print("Log has been sent to Braintrust server")


if __name__ == "__main__":
    main()
