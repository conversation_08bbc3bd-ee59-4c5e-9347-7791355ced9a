#!/usr/bin/env python3
"""
Example of fetching an experiment by ID using the Braintrust SDK.

Since braintrust.init() doesn't directly support fetching by experiment ID,
this script shows a workaround using the lower-level API connection.

Usage:
    python fetch_experiment_by_id.py

Make sure to:
1. Install braintrust: pip install braintrust
2. Set your BRAINTRUST_API_KEY environment variable
3. Update EXPERIMENT_ID below with your actual experiment ID
"""

import braintrust
from braintrust.logger import _state

# Configuration - update with your actual experiment ID
EXPERIMENT_ID = "your-experiment-id-here"  # Replace with actual experiment ID

def fetch_experiment_by_id(experiment_id: str):
    """
    Fetch an experiment by its ID using the Braintrust API directly.
    
    Args:
        experiment_id: The UUID of the experiment to fetch
        
    Returns:
        Dictionary containing experiment metadata
    """
    
    # First, ensure we're logged in
    braintrust.login()
    
    # Use the internal API connection to fetch experiment by ID
    # This is a workaround since braintrust.init() doesn't support experiment_id parameter
    response = _state.app_conn().post_json("api/experiment/get", {
        "id": experiment_id
    })
    
    if len(response) == 0:
        raise ValueError(f"Experiment with ID {experiment_id} not found.")
    
    return response[0]

def fetch_experiment_records_by_id(experiment_id: str, limit: int = 100):
    """
    Fetch experiment records by experiment ID using the API directly.
    
    Args:
        experiment_id: The UUID of the experiment
        limit: Maximum number of records to fetch
        
    Returns:
        List of experiment records
    """
    
    # Ensure we're logged in
    braintrust.login()
    
    # Fetch experiment records using the API
    response = _state.app_conn().post_json("api/experiment/fetch", {
        "id": experiment_id,
        "limit": limit
    })
    
    return response.get("events", [])

def main():
    try:
        print(f"Fetching experiment with ID: {EXPERIMENT_ID}")
        
        # Fetch experiment metadata
        experiment_info = fetch_experiment_by_id(EXPERIMENT_ID)
        
        print("✓ Experiment fetched successfully!")
        print(f"Experiment ID: {experiment_info['id']}")
        print(f"Experiment Name: {experiment_info['name']}")
        print(f"Project ID: {experiment_info['project_id']}")
        print(f"Created: {experiment_info.get('created', 'N/A')}")
        print(f"Description: {experiment_info.get('description', 'N/A')}")
        
        # Now fetch the experiment records
        print(f"\nFetching experiment records...")
        records = fetch_experiment_records_by_id(EXPERIMENT_ID, limit=5)
        
        print(f"Found {len(records)} records (showing up to 5)")
        print("-" * 50)
        
        for i, record in enumerate(records):
            print(f"Record {i+1}:")
            if 'input' in record:
                print(f"  Input: {record['input']}")
            if 'output' in record:
                print(f"  Output: {record['output']}")
            if 'expected' in record:
                print(f"  Expected: {record['expected']}")
            if 'scores' in record:
                print(f"  Scores: {record['scores']}")
            if 'created' in record:
                print(f"  Created: {record['created']}")
            print()
        
        # Alternative: Use the experiment name to fetch with braintrust.init()
        print("=" * 60)
        print("Alternative: Using experiment name with braintrust.init()")
        print("=" * 60)
        
        experiment_name = experiment_info['name']
        
        # We need to get the project name from project_id
        # This requires another API call to get project info
        project_response = _state.app_conn().post_json("api/project/get", {
            "id": experiment_info['project_id']
        })
        
        if len(project_response) > 0:
            project_name = project_response[0]['name']
            
            print(f"Now fetching the same experiment using name: '{experiment_name}' in project: '{project_name}'")
            
            # Use the standard braintrust.init() approach
            experiment = braintrust.init(
                project=project_name,
                experiment=experiment_name,
                open=True
            )
            
            print(f"✓ Successfully fetched using braintrust.init()")
            print(f"Experiment ID matches: {experiment.id == EXPERIMENT_ID}")
            
            # Show a few records using the standard approach
            print("\nRecords using standard approach:")
            for i, record in enumerate(experiment.fetch()):
                if i >= 2:  # Limit to 2 records
                    break
                print(f"Record {i+1}: Input={record.get('input')}, Output={record.get('output')}")
        
    except ValueError as e:
        print(f"❌ Error: {e}")
        print("\nTroubleshooting:")
        print("- Check that the experiment ID is correct")
        print("- Verify you have access to the experiment")
        print("- Make sure the experiment exists")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        print("\nTroubleshooting:")
        print("- Make sure BRAINTRUST_API_KEY environment variable is set")
        print("- Check your internet connection")
        print("- Verify your API key is valid")

def get_experiment_id_from_url(experiment_url: str) -> str:
    """
    Helper function to extract experiment ID from a Braintrust experiment URL.
    
    Example URL: https://www.braintrust.dev/app/org-name/p/project-id/experiments/experiment-id
    
    Args:
        experiment_url: The full URL to the experiment
        
    Returns:
        The experiment ID extracted from the URL
    """
    
    # Extract experiment ID from URL
    # URL format: https://www.braintrust.dev/app/{org}/p/{project_id}/experiments/{experiment_id}
    parts = experiment_url.rstrip('/').split('/')
    if 'experiments' in parts:
        experiment_index = parts.index('experiments')
        if experiment_index + 1 < len(parts):
            return parts[experiment_index + 1]
    
    raise ValueError(f"Could not extract experiment ID from URL: {experiment_url}")

if __name__ == "__main__":
    # Example of extracting ID from URL (uncomment and modify as needed)
    # experiment_url = "https://www.braintrust.dev/app/your-org/p/project-id/experiments/your-experiment-id"
    # EXPERIMENT_ID = get_experiment_id_from_url(experiment_url)
    # print(f"Extracted experiment ID: {EXPERIMENT_ID}")
    
    main()
