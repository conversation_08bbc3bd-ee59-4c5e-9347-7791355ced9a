#!/usr/bin/env python3
"""
Example showing how to use an existing Braintrust experiment as a dataset in a new evaluation.

This demonstrates the complete workflow:
1. Fetch an existing experiment
2. Convert it to dataset format
3. Use it in a new evaluation with a different task function
4. Compare performance against the original experiment

Usage:
    python eval_with_existing_experiment.py
    
Or run with braintrust CLI:
    BRAINTRUST_API_KEY=your_key braintrust eval eval_with_existing_experiment.py
"""

import braintrust
from braintrust import Eval

# Configuration
PROJECT_NAME = "your-project-name"
EXPERIMENT_NAME = "your-experiment-name"

def improved_task_function(input_text):
    """
    Example improved task function - replace this with your actual improved model/function.
    
    This represents a new version of your model that you want to compare
    against the original experiment results.
    """
    # This is a simple example - in practice, this would be your improved LLM call,
    # API request, or model inference
    return f"Improved response: {input_text}"

def exact_match_scorer(input, output, expected):
    """
    Simple scoring function that checks for exact matches.
    """
    return 1.0 if output == expected else 0.0

def contains_scorer(input, output, expected):
    """
    Scoring function that checks if the expected text is contained in the output.
    """
    if expected and output:
        return 1.0 if str(expected).lower() in str(output).lower() else 0.0
    return 0.0

def similarity_scorer(input, output, expected):
    """
    Simple similarity scorer based on common words.
    In practice, you might use more sophisticated similarity measures.
    """
    if not expected or not output:
        return 0.0
    
    expected_words = set(str(expected).lower().split())
    output_words = set(str(output).lower().split())
    
    if not expected_words:
        return 0.0
    
    intersection = expected_words.intersection(output_words)
    return len(intersection) / len(expected_words)

def main():
    """
    Main function that fetches an experiment and uses it as dataset for a new evaluation.
    """
    
    print(f"Fetching existing experiment '{EXPERIMENT_NAME}' from project '{PROJECT_NAME}'...")
    
    # Fetch the existing experiment (read-only)
    existing_experiment = braintrust.init(
        project=PROJECT_NAME,
        experiment=EXPERIMENT_NAME,
        open=True  # Read-only mode
    )
    
    print(f"Experiment '{existing_experiment.name}' loaded successfully!")
    
    # Convert experiment to dataset format
    print("Converting experiment data to dataset format...")
    dataset_from_experiment = list(existing_experiment.as_dataset())
    
    print(f"Found {len(dataset_from_experiment)} records in the experiment")
    
    if len(dataset_from_experiment) == 0:
        print("No records found in the experiment. Make sure the experiment has data.")
        return
    
    # Show a sample of the data
    print("\nSample data from experiment:")
    for i, record in enumerate(dataset_from_experiment[:3]):
        print(f"Record {i+1}:")
        print(f"  Input: {record.get('input')}")
        print(f"  Expected: {record.get('expected')}")
        print()
    
    # Run new evaluation using the experiment data as dataset
    print("Running new evaluation with improved task function...")
    
    result = Eval(
        name="Improved Model vs Original Experiment",
        data=dataset_from_experiment,  # Use the experiment data as dataset
        task=improved_task_function,   # Your improved task function
        scores=[
            exact_match_scorer,
            contains_scorer,
            similarity_scorer,
        ]
    )
    
    print("Evaluation completed!")
    print(f"Results summary: {result}")

def alternative_direct_approach():
    """
    Alternative approach: Use BaseExperiment to directly reference the experiment.
    
    This is more concise and lets Braintrust handle the conversion internally.
    """
    
    from braintrust.framework import BaseExperiment
    
    print("\nTrying alternative approach with BaseExperiment...")
    
    result = Eval(
        name="Direct BaseExperiment Evaluation",
        data=BaseExperiment(name=EXPERIMENT_NAME),  # Direct reference to experiment
        task=improved_task_function,
        scores=[exact_match_scorer, similarity_scorer]
    )
    
    return result

def compare_with_original():
    """
    Example of how you might compare results with the original experiment.
    """
    
    print("\n" + "=" * 60)
    print("Comparing with Original Experiment:")
    print("=" * 60)
    
    # Fetch original experiment
    original_experiment = braintrust.init(
        project=PROJECT_NAME,
        experiment=EXPERIMENT_NAME,
        open=True
    )
    
    # You could analyze the original scores vs new scores here
    print("Original experiment records with scores:")
    
    for i, record in enumerate(original_experiment.fetch()):
        if i >= 3:  # Limit for demo
            break
            
        print(f"Original Record {i+1}:")
        print(f"  Input: {record.get('input')}")
        print(f"  Output: {record.get('output')}")
        print(f"  Scores: {record.get('scores')}")
        print()

if __name__ == "__main__":
    main()
    
    # Uncomment to try the alternative approach
    # print("\n" + "="*60)
    # alternative_result = alternative_direct_approach()
    # print(f"Alternative result: {alternative_result}")
    
    # Uncomment to compare with original
    # compare_with_original()
