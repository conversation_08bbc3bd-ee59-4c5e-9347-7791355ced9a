import os, requests, csv, argparse, sys
from datetime import datetime, timezone
from pathlib import Path

def fetch_dataset_ids(global_parameters):
    
    try:
        datasets = []
        starting_after = None
        limit = 100
        org = global_parameters['org_name']
        project = global_parameters['project_id']

        while True:
            params = {}
            params['limit'] = limit
            params['project_id'] = project
            params['org_name'] = org

            if starting_after:
                params['starting_after'] = starting_after

            response = requests.get(global_parameters['braintrust_url']+global_parameters['dataset_endpoint'], headers=global_parameters['headers'], params=params)
            response.raise_for_status()
            data = response.json()

            # when there are no more datasets, exit the loop
            if data['objects'] == []:
                break
            
            datasets.extend(data.get('objects', []))
            # advancing to next page
            starting_after = data['objects'][-1]['id']

        return datasets
    except requests.exceptions.RequestException as e:
        print(f"API request error: {e}")
        return []
    except (KeyError, IndexError) as e:
        print(f"Data parsing error: {e}")
        return []
    except Exception as e:
        print(f"Unexpected error: {e}")
        return []
    
def fetch_dataset(dataset_id, global_parameters):    
    dataset = []    
    limit = 100
    cursor = None
    
    try:
        while True:
            payload = {}
            payload['limit'] = limit

            if cursor:
                payload['cursor'] = cursor
            
            try:
                response = requests.post(global_parameters['braintrust_url']+global_parameters['dataset_endpoint']+dataset_id+global_parameters['fetch_endpoint'], headers=global_parameters['headers'], json=payload)
                response.raise_for_status()
                data = response.json()
                
                dataset.extend(data.get('events', []))
                # advancing to next page
                cursor = data.get('cursor',False)

                # when there are no more datasets, exit the loop
                if data.get('cursor',False) == False:
                    break
            except requests.exceptions.HTTPError as e:
                print(f"HTTP Error: {e}")
                break
            except requests.exceptions.ConnectionError as e:
                print(f"Connection Error: {e}")
                break
            except requests.exceptions.Timeout as e:
                print(f"Timeout Error: {e}")
                break
            except requests.exceptions.RequestException as e:
                print(f"Request Error: {e}")
                break
            except ValueError as e:
                print(f"JSON parsing error: {e}")
                break
        
        return dataset
    except Exception as e:
        print(f"Unexpected error in fetch_dataset: {e}")
        return []
    
def fetch_experiment_ids(global_parameters):
    try:
        experiments = []
        starting_after = None
        limit = 100
        org = global_parameters['org_name']
        project = global_parameters['project_id']

        while True:
            params = {}
            params['limit'] = limit
            params['project_id'] = project
            params['org_name'] = org

            if starting_after:
                params['starting_after'] = starting_after

            response = requests.get(global_parameters['braintrust_url']+global_parameters['experiment_endpoint'], headers=global_parameters['headers'], params=params)
            response.raise_for_status()
            data = response.json()

            # when there are no more experiments, exit the loop
            if data['objects'] == []:
                break
            
            experiments.extend(data.get('objects', []))
            # advancing to next page
            starting_after = data['objects'][-1]['id']

        return experiments
    except requests.exceptions.RequestException as e:
        print(f"API request error: {e}")
        return []
    except (KeyError, IndexError) as e:
        print(f"Data parsing error: {e}")
        return []
    except Exception as e:
        print(f"Unexpected error: {e}")
        return []
    
def fetch_experiment(experiment_id,global_parameters):    
    headers = {
    'Authorization': f'Bearer {global_parameters['api_key']}'
    }
    experiment = []    
    limit = 100
    cursor = None
    
    try:
        while True:
            payload = {}
            payload['limit'] = limit

            if cursor:
                payload['cursor'] = cursor
            
            try:
                response = requests.post(global_parameters['braintrust_url']+global_parameters['experiment_endpoint']+experiment_id+global_parameters['fetch_endpoint'], headers=headers, json=payload)
                response.raise_for_status()
                data = response.json()
                
                experiment.extend(data.get('events', []))
                # advancing to next page
                cursor = data.get('cursor',False)

                # when there are no more experiments, exit the loop
                if data.get('cursor',False) == False:
                    break
            except requests.exceptions.HTTPError as e:
                print(f"HTTP Error: {e}")
                break
            except requests.exceptions.ConnectionError as e:
                print(f"Connection Error: {e}")
                break
            except requests.exceptions.Timeout as e:
                print(f"Timeout Error: {e}")
                break
            except requests.exceptions.RequestException as e:
                print(f"Request Error: {e}")
                break
            except ValueError as e:
                print(f"JSON parsing error: {e}")
                break
        experiment = group_traces(experiment)
        experiment = format_experiment(experiment)
        return experiment
    except Exception as e:
        print(f"Unexpected error in fetch_experiment: {e}")
        return []

def group_traces(fexperiment):
    grouped_traces = {}
    
    try:
        for event in fexperiment:
            # the root_span_id is the identifier that links spans to the same event
            root_span_id = event.get('root_span_id')
            if root_span_id not in grouped_traces:
                grouped_traces[root_span_id] = []
            grouped_traces[root_span_id].append(event)
        
        return grouped_traces
    except Exception as e:
        print(f"Error in group_traces: {e}")
        return {}

def format_experiment(gexperiment):
    oexperiment = []
    try:
        for events in gexperiment:
            try:
                sevent = {}
                start = True
                end = True
                for trace in gexperiment[events]:
                    try:
                        #######################################################################
                        if trace['span_attributes']['type'] == 'score' or trace['span_attributes']['type'] == 'llm':
                            pass
                        else:
                            #I was looking for error, input, output and expected only in spans with type, now I'm looking at all of the spans except for the score types
                            if trace.get('error','ERROR') != None:
                                sevent['error'] = trace.get('error','ERROR')
                            
                            if 'input' not in sevent:
                                sevent['input'] = trace.get('input','ERROR')
                            elif trace.get('input','ERROR') != None:
                                sevent['input'] = trace.get('input','ERROR')
                            
                            if 'expected' not in sevent:
                                sevent['expected'] = trace.get('expected','ERROR')
                            elif trace.get('expected','ERROR') != None:
                                sevent['expected'] = trace.get('expected','ERROR')
                            
                            if 'output' not in sevent:
                                sevent['output'] = trace.get('output','ERROR')
                            elif trace.get('output','ERROR') != None:
                                sevent['output'] = trace.get('output','ERROR')
                        #######################################################################
                        if trace['span_attributes']['type'] == 'eval':
                            sevent['name'] = trace['span_attributes'].get('name','ERROR')
                            # sevent['input'] = trace.get('input','ERROR')
                            # if trace.get('output','ERROR') == None:
                            #     sevent['output'] = trace.get('error','ERROR')
                            # else:
                            #     sevent['output'] = trace.get('output','ERROR')
                            # sevent['output'] = trace.get('output','ERROR')
                            #######################################################################
                            # sevent['expected'] = trace.get('expected','ERROR')
                            sevent['tags'] = trace.get('tags','ERROR')
                            sevent['metadata'] = str(trace.get('metadata','ERROR'))
                            sevent['created'] = trace.get('created','ERROR')
                            sevent['duration'] = round(trace['metrics'].get('end','ERROR') - trace['metrics'].get('start','ERROR'),1)
                        if trace['span_attributes']['type'] == 'score':
                            sevent['score_type'] = trace['span_attributes'].get('name','ERROR')
                            if trace['scores'] != None:
                                sevent['score'] = trace['scores'].get(sevent['score_type'],'ERROR')
                        if trace['span_attributes']['type'] == 'llm':
                            sevent['llm_duration'] = round(trace['metrics'].get('end','ERROR') - trace['metrics'].get('start','ERROR'),1)
                            # I was capturing the model to try and dynamically calculate the price based on the model but could not find a place where to pull the values
                            # sevent['model'] = trace['metadata'].get('model','ERROR')
                            sevent['prompt_tokens'] = trace['metrics'].get('prompt_tokens','ERROR')
                            sevent['completion_tokens'] = trace['metrics'].get('completion_tokens','ERROR')
                            sevent['tokens'] = trace['metrics'].get('tokens','ERROR')
                            if (sevent['prompt_tokens'] == 'ERROR' or sevent['completion_tokens'] == 'ERROR'):
                                sevent['estimated_cost'] = 'ERROR'
                            else:
                                sevent['estimated_cost'] = estimate_llm_cost(sevent['prompt_tokens'],sevent['completion_tokens'])
                        if start == True:
                            start = trace['metrics'].get('start','ERROR')
                        else:
                            start = min(start, trace['metrics'].get('start','ERROR'))
                        if end == True:
                            end = trace['metrics'].get('end','ERROR')
                        else:
                            end = max(end, trace['metrics'].get('end','ERROR'))
                    except KeyError as e:
                        print(f"Missing key in trace: {e}")
                        continue
                #######################################################################
                if sevent.get('error') != None:
                    sevent['output'] = sevent['error']
                #######################################################################
                sevent['start'] = start
                sevent['end'] = end
                oexperiment.append(sevent)
            except Exception as e:
                print(f"Error processing event {events}: {e}")
                continue
        return oexperiment
    except Exception as e:
        print(f"Error in format_experiment: {e}")
        return []
    
def write_dataset(data, name, output_dir):
    key_map = {
        'input':    'Input',
        'expected': 'Output',
        'tags':     'Tags',
        'metadata': 'Metadata',
        'created':  'Created',
    }

    try:
        # Create full path for the file
        file_name = os.path.join(output_dir, f"{name}.csv")

        with open(file_name, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=list(key_map.values()))
            writer.writeheader()

            for row in data:
                try:
                    # Build a filtered+renamed dict for this row
                    filtered = {}
                    for old_key, new_header in key_map.items():
                        filtered[new_header] = row.get(old_key, '')
                    writer.writerow(filtered)
                except Exception as e:
                    print(f"Error processing row in dataset {name}: {e}")
                    continue
        
        return True
    except IOError as e:
        print(f"I/O error writing dataset {name} to {file_name}: {e}")
        return False
    except Exception as e:
        print(f"Unexpected error writing dataset {name}: {e}")
        return False

def score_column_name(data):
    if data[0].get('score_type', False):
        score_type = data[0]['score_type']
    #this will only happen if everything failed
    else:
        for i in data:
            if i.get('score_type', False):
                score_type = i['score_type']
            else:
                score_type = 'Score'
    return score_type

def write_experiment(data, name, output_dir):
    try:
        score_type = score_column_name(data)
            
        key_map = {
            'name': 'Name',
            'input': 'Input',
            'output': 'Output',
            'expected': 'Expected',
            'tags':'Tags',
            'score': score_type,
            'duration':'Duration',
            'llm_duration':'LLM_Duration',
            'prompt_tokens':'Prompt_tokens',
            'completion_tokens':'Completion Tokens',
            'tokens':'Total tokens',
            'estimated_cost': 'Estimated LLM cost',
            'metadata':'Metadata',
            'created':'Created',
            'start':'Start',
            'end':'End',
        }

        # Create full path for the file
        file_name = os.path.join(output_dir, f"{name}.csv")

        with open(file_name, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=list(key_map.values()))
            writer.writeheader()

            for row in data:
                try:
                    # Build a filtered+renamed dict for this row
                    filtered = {}
                    for old_key, new_header in key_map.items():
                        value = row.get(old_key, '')
                        
                        # Convert epoch timestamps to ISO 8601 format with UTC timezone
                        if old_key == 'start' or old_key == 'end':
                            try:
                                if isinstance(value, (int, float)) and value > 0:
                                    dt = datetime.fromtimestamp(value, tz=timezone.utc)
                                    value = dt.strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'
                            except Exception as e:
                                print(f"Error converting timestamp {value}: {e}")
                        
                        filtered[new_header] = value
                    writer.writerow(filtered)
                except Exception as e:
                    print(f"Error processing row in experiment {name}: {e}")
                    continue
        
        return True
    except IOError as e:
        print(f"I/O error writing experiment {name} to {file_name}: {e}")
        return False
    except Exception as e:
        print(f"Unexpected error writing experiment {name}: {e}")
        return False

def estimate_llm_cost(prompt_tokens, completion_tokens): 
    # Using GPT-4 Turbo pricing (as of 2024) as I didn't find any good place where I can compile most providers and it looks to be about average
    try:
        input_price_per_1k = 0.01
        output_price_per_1k = 0.03
        cost = (float(prompt_tokens) / 1000 * input_price_per_1k) + (float(completion_tokens) / 1000 * output_price_per_1k)
        return round(cost, 5)
    except (ValueError, TypeError, ZeroDivisionError) as e:
        print(f"Error calculating LLM cost: {e}")
        return "ERROR"
    except Exception as e:
        print(f"Unexpected error in cost calculation: {e}")
        return "ERROR"
    
def get_project_name(global_parameters):
    try:
        response = requests.get(global_parameters['braintrust_url']+global_parameters['project_endpoint']+global_parameters['project_id'], headers=global_parameters['headers'])
        response.raise_for_status()
        data = response.json()
        return data['name']
    except Exception as e:
        print(f"Unexpected error fetching project name: {e}")
        print(f"Setting project ID as root folder name")
        return global_parameters['project_id']

def parse_arguments():
    parser = argparse.ArgumentParser(description='Retrieve and process Braintrust datasets and experiments')
    
    parser.add_argument('--api-key', help='Braintrust API key')
    parser.add_argument('--project-id', help='Project ID')
    parser.add_argument('--org-name', help='Organization name')
    parser.add_argument('--braintrust-url', help='Braintrust API URL')
    parser.add_argument('--dataset-endpoint', help='Dataset endpoint path')
    parser.add_argument('--experiment-endpoint', help='Experiment endpoint path')
    parser.add_argument('--fetch-endpoint', help='Fetch endpoint path')
    parser.add_argument('--project-endpoint', help='Project endpoint path')
    parser.add_argument('--output-dir', help='Output directory for project files (default: current directory)')
    
    return parser.parse_args()

def setup_global_parameters():
    args = parse_arguments()
    
    # Set up global parameters with defaults, environment variables, or command line arguments
    global_parameters = {
        'api_key': args.api_key or os.environ.get('BRAINTRUST_API_KEY'),
        'project_id': args.project_id or os.environ.get('BRAINTRUST_PROJECT_ID'),
        'org_name': args.org_name or os.environ.get('BRAINTRUST_ORG_NAME'),
        'braintrust_url': args.braintrust_url or os.environ.get('BRAINTRUST_URL') or 'https://api.braintrust.dev',
        'dataset_endpoint': args.dataset_endpoint or os.environ.get('BRAINTRUST_DATASET_ENDPOINT') or '/v1/dataset/',
        'experiment_endpoint': args.experiment_endpoint or os.environ.get('BRAINTRUST_EXPERIMENT_ENDPOINT') or '/v1/experiment/',
        'fetch_endpoint': args.fetch_endpoint or os.environ.get('BRAINTRUST_FETCH_ENDPOINT') or '/fetch',
        'project_endpoint': args.project_endpoint or os.environ.get('BRAINTRUST_PROJECT_ENDPOINT') or '/v1/project/',
        'output_dir': args.output_dir or os.environ.get('BRAINTRUST_OUTPUT_DIR') or os.path.join('.')
    }
    
    # Validate required parameters
    missing_params = []
    
    if not global_parameters['api_key']:
        missing_params.append("API key (--api-key or BRAINTRUST_API_KEY)")
    
    if not global_parameters['project_id']:
        missing_params.append("Project ID (--project-id or BRAINTRUST_PROJECT_ID)")
    
    if not global_parameters['org_name']:
        missing_params.append("Organization name (--org-name or BRAINTRUST_ORG_NAME)")
    
    if missing_params:
        print("Error: The following required parameters are missing:")
        for param in missing_params:
            print(f"  - {param}")
        print("\nPlease provide these parameters either as command-line arguments or environment variables.")
        print("Example usage:")
        print("  python dataset_experiment_retrieval.py --api-key YOUR_API_KEY --project-id YOUR_PROJECT_ID --org-name YOUR_ORG_NAME")
        print("\nOr set environment variables:")
        print("  export BRAINTRUST_API_KEY=YOUR_API_KEY")
        print("  export BRAINTRUST_PROJECT_ID=YOUR_PROJECT_ID")
        print("  export BRAINTRUST_ORG_NAME=YOUR_ORG_NAME")
        sys.exit(1)
    
    global_parameters['headers'] = {'Authorization': f'Bearer {global_parameters['api_key']}'}
    global_parameters['output_dir'] = os.path.join(global_parameters['output_dir'],get_project_name(global_parameters))

    return global_parameters

def create_directory_structure(base_dir):
    # Create the directory structure for the project
    try:
        project_dir = Path(base_dir)
        project_dir.mkdir(exist_ok=True, parents=True)
        datasets_dir = project_dir / "datasets"
        datasets_dir.mkdir(exist_ok=True)
        experiments_dir = project_dir / "experiments"
        experiments_dir.mkdir(exist_ok=True)
        return {
            'project_dir': str(project_dir),
            'datasets_dir': str(datasets_dir),
            'experiments_dir': str(experiments_dir)
        }
    except Exception as e:
        print(f"Error creating directory structure: {e}")
        sys.exit(1)

def main():
    try:
        # Set up global parameters
        global_parameters = setup_global_parameters()

        # Create directory structure
        dirs = create_directory_structure(global_parameters['output_dir'])
        print(f"Project directory created at: {dirs['project_dir']}")
        print(f"Datasets will be saved to: {dirs['datasets_dir']}")
        print(f"Experiments will be saved to: {dirs['experiments_dir']}")
        
        print(f"\nRetrieving datasets for project {global_parameters['project_id']} in organization {global_parameters['org_name']}...")
        
        # Fetch and process datasets
        dataset_ids = fetch_dataset_ids(global_parameters)
        if not dataset_ids:
            print("No datasets found.")
        else:
            print(f"Found {len(dataset_ids)} datasets.")
            for dataset in dataset_ids:
                try:
                    dataset_name = dataset['name']
                    print(f"Processing dataset: {dataset_name} (ID: {dataset['id']})")
                    dataset_data = fetch_dataset(dataset['id'], global_parameters)
                    if dataset_data:
                        write_dataset(dataset_data, dataset_name, dirs['datasets_dir'])
                        print(f"Successfully wrote dataset {dataset_name} with {len(dataset_data)} records.")
                    else:
                        print(f"No data retrieved for dataset {dataset_name}.")
                except Exception as e:
                    print(f"Error processing dataset {dataset.get('name', 'unknown')}: {e}")
        
        # Fetch and process experiments
        print("\nRetrieving experiments...")
        experiment_ids = fetch_experiment_ids(global_parameters)
        if not experiment_ids:
            print("No experiments found.")
        else:
            print(f"Found {len(experiment_ids)} experiments.")
            for experiment in experiment_ids:
                try:
                    experiment_name = experiment['name']
                    print(f"Processing experiment: {experiment_name} (ID: {experiment['id']})")
                    experiment_data = fetch_experiment(experiment['id'], global_parameters)
                    if experiment_data:
                        write_experiment(experiment_data, experiment_name, dirs['experiments_dir'])
                        print(f"Successfully wrote experiment {experiment_name} with {len(experiment_data)} records.")
                    else:
                        print(f"No data retrieved for experiment {experiment_name}.")
                except Exception as e:
                    print(f"Error processing experiment {experiment.get('name', 'unknown')}: {e}")
        
        print("\nAll processing completed.")
        
    except Exception as e:
        print(f"Fatal error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
