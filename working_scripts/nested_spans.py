#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create a log with deeply nested spans (child spans that have their own child spans).

This demonstrates multi-level hierarchical logging:
- Level 1: Main task span
- Level 2: Major sub-tasks
- Level 3: Detailed steps within sub-tasks
- Level 4: Individual operations within steps

Usage:
    python nested_spans.py

Make sure to set your BRAINTRUST_API_KEY environment variable.
"""

import time
import braintrust


def main():
    # Initialize logger for a project
    logger = braintrust.init_logger(project="pedro-repro4667")
    
    # Level 1: Main task span
    with logger.start_span(name="document_analysis_pipeline") as main_span:
        main_span.log(
            input="Analyze the quarterly financial report document",
            metadata={
                "document_id": "doc_q4_2023_financial",
                "document_type": "financial_report",
                "pages": 45
            }
        )
        
        # Level 2: Document preprocessing
        with main_span.start_span(name="document_preprocessing") as preprocess_span:
            preprocess_span.log(
                input="Raw PDF document with 45 pages",
                metadata={"stage": "preprocessing"}
            )
            
            # Level 3: Text extraction from preprocessing
            with preprocess_span.start_span(name="text_extraction") as extract_span:
                extract_span.log(
                    input="PDF file with mixed content",
                    metadata={"extraction_method": "OCR + text_layer"}
                )
                
                # Level 4: Individual page processing
                with extract_span.start_span(name="page_1_extraction") as page1_span:
                    page1_span.log(
                        input="Page 1: Executive Summary",
                        output="Extracted 1,247 characters",
                        scores={"text_quality": 0.98, "confidence": 0.95}
                    )
                
                with extract_span.start_span(name="page_2_extraction") as page2_span:
                    page2_span.log(
                        input="Page 2: Financial Overview",
                        output="Extracted 1,456 characters",
                        scores={"text_quality": 0.94, "confidence": 0.92}
                    )
                
                # Log extraction summary
                extract_span.log(
                    output="Successfully extracted text from 45 pages",
                    scores={"overall_quality": 0.96, "pages_processed": 0.45}
                )
            
            # Level 3: Text cleaning from preprocessing
            with preprocess_span.start_span(name="text_cleaning") as clean_span:
                clean_span.log(
                    input="Raw extracted text with formatting artifacts",
                    metadata={"cleaning_steps": ["remove_headers", "fix_spacing", "normalize_encoding"]}
                )
                
                # Level 4: Individual cleaning steps
                with clean_span.start_span(name="remove_headers_footers") as headers_span:
                    headers_span.log(
                        input="Text with page headers and footers",
                        output="Clean text without headers/footers",
                        scores={"artifacts_removed": 0.99}
                    )
                
                with clean_span.start_span(name="normalize_whitespace") as whitespace_span:
                    whitespace_span.log(
                        input="Text with irregular spacing",
                        output="Text with normalized spacing",
                        scores={"formatting_quality": 0.97}
                    )
                
                clean_span.log(
                    output="Cleaned and normalized text ready for analysis",
                    scores={"cleaning_quality": 0.95}
                )
            
            preprocess_span.log(
                output="Document preprocessing completed",
                scores={"preprocessing_success": 1.0}
            )
        
        # Level 2: Content analysis
        with main_span.start_span(name="content_analysis") as analysis_span:
            analysis_span.log(
                input="Cleaned document text",
                metadata={"analysis_type": "financial_metrics_extraction"}
            )
            
            # Level 3: Financial metrics extraction
            with analysis_span.start_span(name="financial_metrics_extraction") as metrics_span:
                metrics_span.log(
                    input="Financial report text",
                    metadata={"target_metrics": ["revenue", "profit", "expenses", "growth_rate"]}
                )
                
                # Level 4: Individual metric extraction
                with metrics_span.start_span(name="revenue_extraction") as revenue_span:
                    revenue_span.log(
                        input="Text sections mentioning revenue",
                        output={"q4_revenue": "$2.4M", "yoy_growth": "15%"},
                        scores={"extraction_confidence": 0.94}
                    )
                
                with metrics_span.start_span(name="profit_extraction") as profit_span:
                    profit_span.log(
                        input="Text sections mentioning profit/loss",
                        output={"net_profit": "$340K", "profit_margin": "14.2%"},
                        scores={"extraction_confidence": 0.91}
                    )
                
                metrics_span.log(
                    output="Successfully extracted key financial metrics",
                    scores={"metrics_completeness": 0.88}
                )
            
            # Level 3: Sentiment analysis
            with analysis_span.start_span(name="sentiment_analysis") as sentiment_span:
                sentiment_span.log(
                    input="Financial report narrative sections",
                    metadata={"analysis_sections": ["executive_summary", "outlook", "risks"]}
                )
                
                # Level 4: Section-specific sentiment
                with sentiment_span.start_span(name="executive_summary_sentiment") as exec_sentiment_span:
                    exec_sentiment_span.log(
                        input="Executive summary text",
                        output={"sentiment": "positive", "confidence": 0.82},
                        scores={"positivity": 0.7}
                    )
                
                with sentiment_span.start_span(name="outlook_sentiment") as outlook_sentiment_span:
                    outlook_sentiment_span.log(
                        input="Future outlook section",
                        output={"sentiment": "cautiously_optimistic", "confidence": 0.76},
                        scores={"positivity": 0.6}
                    )
                
                sentiment_span.log(
                    output="Overall document sentiment: positive with caution",
                    scores={"overall_sentiment": 0.65}
                )
            
            analysis_span.log(
                output="Content analysis completed",
                scores={"analysis_quality": 0.89}
            )
        
        # Level 2: Report generation
        with main_span.start_span(name="report_generation") as report_span:
            report_span.log(
                input="Extracted metrics and sentiment analysis",
                metadata={"report_format": "executive_summary"}
            )
            
            # Level 3: Summary creation
            with report_span.start_span(name="summary_creation") as summary_span:
                summary_span.log(
                    input="Financial metrics and sentiment data",
                    output="Q4 2023 showed strong performance with $2.4M revenue (15% YoY growth) and healthy 14.2% profit margin. Overall sentiment is positive with cautious optimism for future quarters.",
                    scores={"summary_quality": 0.92, "conciseness": 0.88}
                )
            
            report_span.log(
                output="Executive summary report generated",
                scores={"report_completeness": 0.94}
            )
        
        # Log final result on main span
        main_span.log(
            output="Document analysis pipeline completed successfully",
            expected="Comprehensive financial document analysis with extracted metrics and insights",
            scores={
                "pipeline_success": 1.0,
                "overall_quality": 0.91,
                "processing_efficiency": 0.87
            },
            metadata={
                "total_spans_created": 15,
                "max_nesting_depth": 4,
                "pipeline_completed": True
            },
            tags=["document_analysis", "financial_report", "multi_level_processing"]
        )
    
    print("Created deeply nested trace structure:")
    print("Level 1: document_analysis_pipeline (main)")
    print("├── Level 2: document_preprocessing")
    print("│   ├── Level 3: text_extraction")
    print("│   │   ├── Level 4: page_1_extraction")
    print("│   │   └── Level 4: page_2_extraction")
    print("│   └── Level 3: text_cleaning")
    print("│       ├── Level 4: remove_headers_footers")
    print("│       └── Level 4: normalize_whitespace")
    print("├── Level 2: content_analysis")
    print("│   ├── Level 3: financial_metrics_extraction")
    print("│   │   ├── Level 4: revenue_extraction")
    print("│   │   └── Level 4: profit_extraction")
    print("│   └── Level 3: sentiment_analysis")
    print("│       ├── Level 4: executive_summary_sentiment")
    print("│       └── Level 4: outlook_sentiment")
    print("└── Level 2: report_generation")
    print("    └── Level 3: summary_creation")
    
    # Flush to ensure all logs are sent to the server
    logger.flush()
    print("\nAll nested spans have been sent to Braintrust server")
    print("Check your Braintrust dashboard to explore the hierarchical trace structure")


if __name__ == "__main__":
    main()
