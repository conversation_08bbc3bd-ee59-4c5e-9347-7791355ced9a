#!/usr/bin/env python3
"""
Simple script to create a dataset with 2 rows having different tags.
"""

import braintrust

def main():
    # Initialize the dataset in the specified project
    dataset = braintrust.init_dataset(
        project="pedro-project1",
        name="sample-dataset-with-tags"
    )
    
    id1 = dataset.insert(
        input="foo",
        expected="bar",
        tags=["tag1"]
    )
    
    id2 = dataset.insert(
        input="foo", 
        expected="bar",
        tags=["tag2"]
    )
    
    dataset.flush()
    
    # Display the records to verify
    print("\nDataset records:")
    for record in dataset:
        print(f"ID: {record['id']}, Input: {record['input']}, Expected: {record['expected']}, Tags: {record['tags']}")

if __name__ == "__main__":
    main()
