import requests
import json

def edit_experiment_tags(experiment_id,token):
    url = f"https://api.braintrust.dev/v1/experiment/{experiment_id}"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    payload = {
        "tags": [
            "red","test123"
        ]
    }

    response = requests.patch(url, headers=headers, data=json.dumps(payload))

    if response.ok:
        print(f"Successfully updated tags for experiment: {experiment_id}")
        return 0
    else:
        print("Failed with status code:", response.status_code)
        return 1

edit_experiment_tags("a7770e09-8736-4d32-b67e-f7051c9d5d66","sk-in91pI0YSJ2ydhPAVvuJ16U33MlcE6gl0HxT5DI5tk2wsIoU")