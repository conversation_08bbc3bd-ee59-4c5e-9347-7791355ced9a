# Requirements for demo_strands_workflow.py
# Package versions from conda environment: production/trust/agentic/base:0.2.0

# Core Strands framework
strands-agents==1.5.0
strands-agents-tools==0.2.9

#Braintrust integration
braintrust==0.2.9

# Data validation
pydantic==2.11.9

# Environment variables
python-dotenv==1.1.1

# LLM integration
litellm==1.75.9
# airbnb_litellm # only for Airbnb internal testing; external users should remove this line

# OpenTelemetry observability
opentelemetry-api==1.37.0
opentelemetry-exporter-otlp-proto-http==1.37.0
opentelemetry-exporter-otlp-proto-grpc==1.37.0
opentelemetry-sdk==1.37.0

# Note: The script includes airbnb_litellm imports which are Airbnb-specific.
# External users should:
# 1. Comment out lines 72-74 in demo_strands_workflow.py (the airbnb_litellm import and initialize call)
# 2. Configure your LiteLLM credentials in the .env file
# 3. Set up appropriate API keys for your LLM provider (e.g., AWS Bedrock, OpenAI, etc.)
