from datetime import datetime
from braintrust import Eval

def task_with_date(input, hooks):
    # Inject current date into metadata
    hooks.metadata["current_date"] = datetime.now().strftime("%Y-%m-%d")
    hooks.metadata["evaluation_timestamp"] = datetime.now().isoformat()
    
    # Your LLM call here
    return f"Today is {hooks.metadata['current_date']}. Hi {input}"

def date_aware_scorer(input, output, expected, metadata):
    current_date = metadata.get("current_date")
    # Use current_date in your scoring logic
    return {
        "score": 1.0 if current_date in output else 0.0,
        "name": "date_awareness",
        "metadata": {"used_date": current_date}
    }

Eval(
    "Pedro-Date Aware Bot",
    data=lambda: [{"input": "<PERSON>", "expected": "Hi David"}],
    task=task_with_date,
    scores=[date_aware_scorer],
)